{"extends": "./src/.umi/tsconfig.json", "compilerOptions": {"target": "ES2020", "lib": ["DOM", "DOM.Iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/utils/*": ["src/utils/*"], "@/services/*": ["src/services/*"], "@/config/*": ["src/config/*"], "@/types/*": ["src/types/*"], "@/constants/*": ["src/constants/*"]}}, "include": ["src/**/*", "mock/**/*", "typings.d.ts"], "exclude": ["node_modules", "dist", "build", "**/*.test.ts", "**/*.test.tsx"]}