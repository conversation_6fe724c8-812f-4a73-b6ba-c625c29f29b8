import { defineConfig } from '@umijs/max';

export default defineConfig({
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  layout: {
    title: 'Front Logix Admin',
  },
  routes: [
    {
      path: '/',
      component: './RootRedirect',
      layout: false,
      hideInMenu: true,
      wrappers: ['@/layouts/AuthLayout'],
    },
    // 认证相关页面（不显示在菜单中）
    {
      name: '登录',
      path: '/login',
      component: './Login',
      layout: false,
      hideInMenu: true,
    },
    {
      name: '注册',
      path: '/register',
      component: './Register',
      layout: false,
      hideInMenu: true,
    },
    {
      name: '重置密码',
      path: '/reset-password',
      component: './ResetPassword',
      layout: false,
      hideInMenu: true,
    },
    // 主要业务页面
    {
      name: '首页',
      path: '/home',
      component: './Home',
      icon: 'home',
      wrappers: ['@/layouts/AuthLayout'],
    },
    // 冰块订单管理模块
    {
      name: '冰块订单管理',
      path: '/ice-order',
      icon: 'shopping',
      wrappers: ['@/layouts/AuthLayout'],
      routes: [
        {
          name: '订单管理',
          path: '/ice-order/orders',
          component: './IceOrder/Orders',
        },
        {
          name: '智能检索',
          path: '/ice-order/search',
          component: './IceOrder/SearchSystem',
        },
        {
          name: '供应商管理',
          path: '/ice-order/suppliers',
          component: './IceOrder/SupplierManagement',
        },
        {
          name: '工厂管理',
          path: '/ice-order/factories',
          component: './IceOrder/FactoryManagement',
        },
        {
          name: '冰块类型',
          path: '/ice-order/ice-types',
          component: './IceOrder/IceTypes',
        },
        {
          name: '用户管理',
          path: '/ice-order/users',
          component: './IceOrder/Users',
        },
        {
          name: '定价管理',
          path: '/ice-order/pricing',
          component: './IceOrder/PricingManagement',
        },
        {
          name: '数据看板',
          path: '/ice-order/dashboard',
          component: './IceOrder/Dashboard',
        },
      ],
    },
    // 产品管理
    {
      name: '产品管理',
      path: '/product-management',
      component: './ProductManagement',
      icon: 'appstore',
      wrappers: ['@/layouts/AuthLayout'],
    },
    // 监控报表
    {
      name: '监控报表',
      path: '/monitoring-report',
      component: './MonitoringReport',
      icon: 'lineChart',
      wrappers: ['@/layouts/AuthLayout'],
    },
    // 运营管理
    {
      name: '运营管理',
      path: '/operation',
      icon: 'team',
      wrappers: ['@/layouts/AuthLayout'],
      routes: [
        {
          name: '内容创建',
          path: '/operation/content-center-create',
          component: './Operation/ContentCenter/Create',
        },
      ],
    },
    // 系统管理
    {
      name: '系统管理',
      path: '/system',
      icon: 'setting',
      wrappers: ['@/layouts/AuthLayout'],
      routes: [
        {
          name: '权限管理',
          path: '/system/permissions',
          component: './PermissionManagement',
        },
        {
          name: '操作日志',
          path: '/system/operation-log',
          component: './System/OperationLog',
        },
      ],
    },
    // 开发示例（可选显示）
    {
      name: '开发示例',
      path: '/examples',
      icon: 'code',
      wrappers: ['@/layouts/AuthLayout'],
      routes: [
        {
          name: '权限演示',
          path: '/examples/access',
          component: './Access',
        },
        {
          name: 'CRUD 示例',
          path: '/examples/table',
          component: './Table',
        },
        {
          name: 'CRUD 示例2',
          path: '/examples/crud-example',
          component: './CrudExample',
        },
      ],
    },
    // 个人中心（不显示在主菜单中）
    {
      name: '个人中心',
      path: '/profile',
      component: './Profile',
      hideInMenu: true,
      wrappers: ['@/layouts/AuthLayout'],
    },
    // 测试页面（开发调试用）
    {
      name: '重定向测试',
      path: '/test-redirect',
      component: './TestRedirect',
      hideInMenu: true,
    },
    {
      name: '调试测试',
      path: '/debug-test',
      component: './DebugTest',
      hideInMenu: true,
    },
    // 错误页面
    {
      name: '404',
      path: '/404',
      component: './NotFound',
      layout: false,
      hideInMenu: true,
    },
    {
      path: '*',
      redirect: '/404',
    },
  ],
  npmClient: 'yarn',
});
