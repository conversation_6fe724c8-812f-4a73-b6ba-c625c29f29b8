import { defineConfig } from '@umijs/max';
import { resolve } from 'path';

// 获取环境变量
const { UMI_ENV } = process.env;

// 基础配置
const baseConfig = {
  // 应用标题
  title: 'Front-Logix-Admin',

  // 主题配置
  theme: {
    '@primary-color': '#1890ff',
  },

  // 国际化配置
  locale: {
    default: 'zh-CN',
    antd: true,
    title: false,
    baseNavigator: true,
    baseSeparator: '-',
  },

  // 路由配置
  history: {
    type: 'browser',
  },

  // 构建配置
  targets: {
    ie: 11,
  },

  // 安全配置
  hash: true,

  // 性能优化
  mfsu: {
    strategy: 'normal',
    shared: {
      'react-json-view': {
        singleton: true,
      },
    },
  },

  // 构建分析
  analyze: {
    analyzerMode: process.env.ANALYZE ? 'server' : 'none',
    analyzerPort: 8888,
    openAnalyzer: true,
    generateStatsFile: !!process.env.ANALYZE_DUMP,
    statsFilename: process.env.ANALYZE_DUMP || 'stats.json',
  },
};

// 环境特定配置
const envConfigs = {
  // 开发环境配置
  development: {
    vite: {
      build: {
        sourcemap: 'inline',
      },
    },
    devServer: {
      port: 8001,
      open: true,
      host: 'localhost',
    },
    mock: {
      exclude: [],
    },
    theme: {
      '@primary-color': '#1DA57A',
    },
  },

  // 测试环境配置
  test: {
    vite: {
      build: {
        sourcemap: true,
      },
    },
    mock: false,
  },

  // 生产环境配置
  production: {
    vite: {
      build: {
        sourcemap: false,
      },
    },
    mock: false,
    extraBabelPlugins: [
      ['transform-remove-console', { exclude: ['error', 'warn'] }],
    ],
    mfsu: true,
    outputPath: 'dist',
    publicPath: '/',
    hash: true,
  },
};

// 合并环境配置
const envConfig = envConfigs[UMI_ENV] || {};

export default defineConfig({
  ...baseConfig,
  ...envConfig,
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  layout: {
    title: '@umijs/max',
  },
  routes: [
    {
      path: '/login',
      component: '@/pages/Login/index',
    },
    {
      path: '/register',
      component: '@/pages/Register/index',
    },
    {
      path: '/reset-password',
      component: '@/pages/ResetPassword/index',
    },
    {
      path: '/',
      redirect: '/home',
    },
    {
      name: '首页',
      path: '/home',
      component: './Home',
    },
    {
      name: '权限演示',
      path: '/access',
      component: './Access',
    },
    {
      name: ' CRUD 示例',
      path: '/table',
      component: './Table',
    },
    {
      name: '监控数据报表',
      path: '/monitoring-report',
      component: './MonitoringReport',
    },
    {
      name: '产品管理',
      path: '/product-management',
      component: './ProductManagement',
    },
    {
      name: '冰块订单管理',
      path: '/ice-order',
      routes: [
        {
          path: '/ice-order',
          redirect: '/ice-order/dashboard',
        },
        {
          name: '订单概览',
          path: '/ice-order/dashboard',
          component: './IceOrder/Dashboard',
        },
        {
          name: '用户管理',
          path: '/ice-order/users',
          component: './IceOrder/Users',
        },
        {
          name: '冰块类型管理',
          path: '/ice-order/ice-types',
          component: './IceOrder/IceTypes',
        },
        {
          name: '订单管理',
          path: '/ice-order/orders',
          component: './IceOrder/Orders',
        },
        {
          name: '工厂管理',
          path: '/ice-order/factory-management',
          component: './IceOrder/FactoryManagement',
        },
        {
          name: '供应商管理',
          path: '/ice-order/supplier-management',
          component: './IceOrder/SupplierManagement',
        },
        {
          name: '动态定价',
          path: '/ice-order/pricing-management',
          component: './IceOrder/PricingManagement',
        },
        {
          name: '智能检索',
          path: '/ice-order/search-system',
          component: './IceOrder/SearchSystem',
        },
      ],
    },
    {
      name: '个人中心',
      path: '/profile',
      component: '@/pages/Profile/index',
    },
    {
      name: '权限管理',
      path: '/permission-management',
      component: '@/pages/PermissionManagement',
      access: 'canAdmin',
    },
    {
      name: '系统管理',
      path: '/system',
      routes: [
        {
          name: '操作日志',
          path: '/system/operation-log',
          component: '@/pages/System/OperationLog',
          access: 'canAdmin',
        },
      ],
    },
  ],
  npmClient: 'yarn',
  define: {
    'process.env.UMI_ENV': UMI_ENV,
  },
  proxy:
    UMI_ENV === 'development'
      ? {
          '/api': {
            target: 'http://localhost:3000',
            changeOrigin: true,
            pathRewrite: { '^/api': '' },
          },
        }
      : {},
  alias: {
    '@config': resolve(__dirname, './src/config'),
    '@utils': resolve(__dirname, './src/utils'),
    '@services': resolve(__dirname, './src/services'),
    '@components': resolve(__dirname, './src/components'),
  },
});
