import { defineConfig } from '@umijs/max';

export default defineConfig({
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  layout: {
    title: 'Front Logix Admin',
  },
  routes: [
    {
      path: '/',
      redirect: '/home',
    },
    {
      name: '登录',
      path: '/login',
      component: './Login',
      layout: false, // 登录页面不使用默认布局
    },
    {
      name: '注册',
      path: '/register',
      component: './Register',
      layout: false, // 注册页面不使用默认布局
    },
    {
      name: '重置密码',
      path: '/reset-password',
      component: './ResetPassword',
      layout: false, // 重置密码页面不使用默认布局
    },
    {
      name: '首页',
      path: '/home',
      component: './Home',
    },
    {
      name: '权限演示',
      path: '/access',
      component: './Access',
    },
    {
      name: 'CRUD 示例',
      path: '/table',
      component: './Table',
    },
    {
      name: '用户资料',
      path: '/profile',
      component: './Profile',
    },
    {
      name: 'CRUD 示例',
      path: '/crud-example',
      component: './CrudExample',
    },
    {
      name: '权限管理',
      path: '/crud-example/permissions',
      component: './PermissionManagement',
    },
    {
      name: '404',
      path: '/404',
      component: './NotFound',
      layout: false,
    },
    {
      path: '*',
      redirect: '/404',
    },
  ],
  npmClient: 'yarn',
});
