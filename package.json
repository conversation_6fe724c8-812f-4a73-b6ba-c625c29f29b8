{"private": true, "author": "", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "analyze:bundle": "cross-env ANALYZE=1 ANALYZE_DUMP=stats.json max build", "analyze:deps": "bash scripts/analyze-dependencies.sh", "build": "max build", "build:prod": "cross-env UMI_ENV=production max build", "build:test": "cross-env UMI_ENV=test max build", "dev": "max dev", "dev:test": "cross-env UMI_ENV=test max dev", "docker:build": "bash scripts/docker-build.sh", "docker:build:dev": "bash scripts/docker-build.sh development", "docker:build:optimized": "bash scripts/docker-build-optimized.sh", "docker:build:optimized:dev": "bash scripts/docker-build-optimized.sh development", "docker:build:optimized:prod": "bash scripts/docker-build-optimized.sh production", "docker:build:optimized:test": "bash scripts/docker-build-optimized.sh test", "docker:build:prod": "bash scripts/docker-build.sh production", "docker:build:test": "bash scripts/docker-build.sh test", "docker:compose": "docker-compose up -d", "docker:compose:dev": "docker-compose up -d front-logix-admin-dev", "docker:compose:local": "IMAGE_NAME=$(grep APP_NAME .env | cut -d= -f2 | tr -d '\r' | tr '[:upper:]' '[:lower:]' | tr ' -' '_') ENV=latest docker-compose -f docker-compose.local.yml up -d", "docker:compose:local:dev": "IMAGE_NAME=$(grep APP_NAME .env | cut -d= -f2 | tr -d '\r' | tr '[:upper:]' '[:lower:]' | tr ' -' '_') ENV=development docker-compose -f docker-compose.local.yml up -d", "docker:compose:local:prod": "IMAGE_NAME=$(grep APP_NAME .env | cut -d= -f2 | tr -d '\r' | tr '[:upper:]' '[:lower:]' | tr ' -' '_') ENV=production docker-compose -f docker-compose.local.yml up -d", "docker:compose:local:test": "IMAGE_NAME=$(grep APP_NAME .env | cut -d= -f2 | tr -d '\r' | tr '[:upper:]' '[:lower:]' | tr ' -' '_') ENV=test docker-compose -f docker-compose.local.yml up -d", "docker:compose:optimized": "IMAGE_NAME=$(grep APP_NAME .env | cut -d= -f2 | tr -d '\r' | tr '[:upper:]' '[:lower:]' | tr ' -' '_') ENV=production docker-compose -f docker-compose.optimized.yml up -d", "docker:compose:optimized:dev": "IMAGE_NAME=$(grep APP_NAME .env | cut -d= -f2 | tr -d '\r' | tr '[:upper:]' '[:lower:]' | tr ' -' '_') ENV=development docker-compose -f docker-compose.optimized.yml up -d", "docker:compose:optimized:prod": "IMAGE_NAME=$(grep APP_NAME .env | cut -d= -f2 | tr -d '\r' | tr '[:upper:]' '[:lower:]' | tr ' -' '_') ENV=production docker-compose -f docker-compose.optimized.yml up -d", "docker:compose:optimized:test": "IMAGE_NAME=$(grep APP_NAME .env | cut -d= -f2 | tr -d '\r' | tr '[:upper:]' '[:lower:]' | tr ' -' '_') ENV=test docker-compose -f docker-compose.optimized.yml up -d", "docker:compose:prod": "docker-compose up -d front-logix-admin-prod", "docker:compose:test": "docker-compose up -d front-logix-admin-test", "docker:run": "bash scripts/docker-run.sh", "docker:run:dev": "bash scripts/docker-run.sh development", "docker:run:local": "bash scripts/docker-run-local.sh", "docker:run:local:dev": "bash scripts/docker-run-local.sh development", "docker:run:local:prod": "bash scripts/docker-run-local.sh production", "docker:run:local:test": "bash scripts/docker-run-local.sh test", "docker:run:optimized": "bash scripts/docker-run-optimized.sh", "docker:run:optimized:dev": "bash scripts/docker-run-optimized.sh development", "docker:run:optimized:prod": "bash scripts/docker-run-optimized.sh production", "docker:run:optimized:test": "bash scripts/docker-run-optimized.sh test", "docker:run:prod": "bash scripts/docker-run.sh production", "docker:run:test": "bash scripts/docker-run.sh test", "format": "prettier --cache --write .", "postinstall": "max setup", "prepare": "husky install", "setup": "max setup", "start": "npm run dev", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@ant-design/charts": "^2.3.0", "@ant-design/icons": "^5.0.1", "@ant-design/pro-components": "^2.4.4", "@types/express": "^5.0.2", "@types/lodash": "^4.17.17", "@umijs/max": "^4.4.11", "antd": "^5.25.2", "axios": "^1.7.0", "react-json-view": "^1.21.3", "redoc-cli": "^0.13.21"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^22.15.29", "@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "cross-env": "^7.0.3", "depcheck": "^1.4.7", "dotenv": "^16.3.1", "dotenv-cli": "^7.3.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "lint-staged": "^13.2.0", "prettier": "^2.8.7", "prettier-plugin-organize-imports": "^3.2.2", "prettier-plugin-packagejson": "^2.4.3", "ts-jest": "^29.3.4", "typescript": "^5.0.3"}}