# 🔧 根路径导航问题排查指南

## 🚨 问题描述

用户反馈：删除浏览器 cookie 后，访问 `http://localhost:8000/` 没有跳转到登录页面。

## 🔍 问题分析

### 1. **端口问题** ⚠️
- **用户访问的地址**: `http://localhost:8000/`
- **实际服务器地址**: `http://localhost:8001/`
- **问题**: 端口不匹配，访问了错误的地址

### 2. **可能的原因**
- 端口 8000 可能有其他服务在运行
- 浏览器缓存了旧的地址
- 用户记忆中的端口号不正确

## ✅ 解决方案

### 步骤 1: 确认正确的服务器地址

**正确地址**: `http://localhost:8001/`

请访问以下地址进行测试：
- 🔗 [http://localhost:8001/](http://localhost:8001/) - 根路径（应该重定向）
- 🔗 [http://localhost:8001/test-redirect](http://localhost:8001/test-redirect) - 测试页面
- 🔗 [http://localhost:8001/login](http://localhost:8001/login) - 登录页面

### 步骤 2: 清除浏览器存储

在浏览器控制台执行：
```javascript
// 清除所有本地存储
localStorage.clear();
sessionStorage.clear();

// 检查清除结果
console.log('localStorage cleared:', Object.keys(localStorage).length === 0);
console.log('sessionStorage cleared:', Object.keys(sessionStorage).length === 0);
```

### 步骤 3: 测试重定向逻辑

1. **访问测试页面**: [http://localhost:8001/test-redirect](http://localhost:8001/test-redirect)
2. **确认未登录状态**: 页面应显示"❌ 未登录"
3. **点击"跳转到根路径"按钮**
4. **预期结果**: 应该跳转到 `/login` 页面

### 步骤 4: 验证登录后的重定向

1. **在测试页面点击"模拟登录"**
2. **确认已登录状态**: 页面应显示"✅ 已登录"
3. **点击"跳转到根路径"按钮**
4. **预期结果**: 应该跳转到 `/home` 页面

## 🧪 测试工具

### 在线测试页面
打开 `test-navigation.html` 文件进行交互式测试。

### 浏览器控制台测试
```javascript
// 检查登录状态
function checkAuth() {
  const token = localStorage.getItem('front_logix_token') || 
               localStorage.getItem('token') || 
               localStorage.getItem('auth_token');
  
  const userInfoStr = localStorage.getItem('userInfo');
  let userInfo = null;
  
  if (userInfoStr) {
    try {
      userInfo = JSON.parse(userInfoStr);
    } catch (e) {
      console.error('用户信息解析失败:', e);
    }
  }

  const hasValidToken = !!(token && token.trim() !== '');
  const hasValidUserInfo = !!(userInfo && (userInfo.name || userInfo.id || userInfo.username));
  const isLoggedIn = hasValidToken && hasValidUserInfo;

  console.group('🔍 认证状态检查');
  console.log('Token 有效:', hasValidToken);
  console.log('用户信息有效:', hasValidUserInfo);
  console.log('登录状态:', isLoggedIn);
  console.log('用户信息:', userInfo);
  console.groupEnd();
  
  return isLoggedIn;
}

// 执行检查
checkAuth();
```

## 📊 预期行为表

| 访问地址 | 登录状态 | 预期跳转 | 实际结果 |
|---------|---------|---------|---------|
| `http://localhost:8001/` | 未登录 | → `/login` | ⏳ 待测试 |
| `http://localhost:8001/` | 已登录 | → `/home` | ⏳ 待测试 |

## 🐛 常见问题

### Q1: 访问 8000 端口没有响应
**A**: 请使用正确的端口 8001

### Q2: 页面显示 404 错误
**A**: 确保开发服务器正在运行，检查控制台是否有错误

### Q3: 重定向没有发生
**A**: 
1. 检查浏览器控制台是否有 JavaScript 错误
2. 确认 localStorage 已清除
3. 检查网络面板是否有请求失败

### Q4: 重定向到错误页面
**A**: 检查用户信息和角色设置是否正确

## 🔧 调试信息

### 开发环境日志
访问根路径时，浏览器控制台应该显示：

```
[Root Redirect Debug]
登录状态检查: {
  isLoggedIn: false,
  userInfo: null,
  currentPath: "/"
}
用户未登录，跳转到登录页
```

### 服务器状态
确认服务器正常运行：
```bash
# 检查服务器状态
curl http://localhost:8001/

# 应该返回 HTML 内容，不是错误信息
```

## 📞 获取帮助

如果问题仍然存在，请提供以下信息：

1. **访问的具体地址**
2. **浏览器控制台的错误信息**
3. **网络面板的请求状态**
4. **localStorage 的内容**
5. **浏览器类型和版本**

---

**最后更新**: 2024年12月  
**状态**: ✅ 服务器运行正常，等待用户测试反馈
