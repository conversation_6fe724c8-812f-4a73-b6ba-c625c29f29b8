# 🧪 根路径导航逻辑测试指南

## 📋 测试目标

验证根路径 `/` 的智能导航逻辑是否按预期工作：
- 未登录用户 → 跳转到 `/login`
- 已登录用户 → 根据角色跳转到对应页面

## 🔧 测试环境

- **开发服务器**: http://localhost:8001
- **测试浏览器**: Chrome/Firefox/Safari
- **测试工具**: 浏览器开发者工具

## 📝 测试步骤

### 1. 测试未登录状态

1. **清除浏览器存储**
   ```javascript
   // 在浏览器控制台执行
   localStorage.clear();
   sessionStorage.clear();
   ```

2. **访问根路径**
   - 在地址栏输入: `http://localhost:8001/`
   - 按回车访问

3. **预期结果**
   - 应该自动跳转到 `/login` 页面
   - 地址栏显示: `http://localhost:8001/login`
   - 页面显示登录表单

### 2. 测试普通用户登录

1. **在登录页面输入普通用户信息**
   - 用户名: `test-user`
   - 密码: `任意密码`
   - 点击登录

2. **预期结果**
   - 登录成功后自动跳转到 `/home` 页面
   - 地址栏显示: `http://localhost:8001/home`
   - 页面显示首页内容

3. **再次访问根路径**
   - 在地址栏输入: `http://localhost:8001/`
   - 按回车访问

4. **预期结果**
   - 应该自动跳转到 `/home` 页面（因为已登录）

### 3. 测试管理员用户登录

1. **清除存储并重新登录**
   ```javascript
   localStorage.clear();
   ```

2. **在登录页面输入管理员信息**
   - 用户名: `frost-chain-admin`
   - 密码: `任意密码`
   - 点击登录

3. **预期结果**
   - 登录成功后自动跳转到 `/home` 页面
   - 用户信息显示为管理员权限

### 4. 测试登出功能

1. **点击右上角用户头像**
   - 选择"退出登录"

2. **预期结果**
   - 自动跳转到 `/login` 页面
   - 本地存储被清除
   - 用户状态重置

## 🔍 调试信息

在浏览器控制台中可以看到以下调试信息：

### 未登录时访问根路径
```
[Root Redirect Debug]
登录状态检查: {
  isLoggedIn: false,
  userInfo: null,
  currentPath: "/"
}
用户未登录，跳转到登录页
```

### 已登录时访问根路径
```
[Root Redirect Debug]
登录状态检查: {
  isLoggedIn: true,
  userInfo: { name: "test-user", ... },
  currentPath: "/"
}
用户已登录，跳转到: /home
```

## ✅ 验证清单

- [ ] 未登录访问 `/` 自动跳转到 `/login`
- [ ] 普通用户登录后跳转到 `/home`
- [ ] 管理员用户登录后跳转到 `/home`
- [ ] 已登录用户访问 `/` 自动跳转到对应页面
- [ ] 登出功能正常工作
- [ ] 页面无错误信息
- [ ] 控制台调试信息正确

## 🐛 常见问题

### 问题1: 页面无限重定向
**原因**: 可能是认证逻辑有问题
**解决**: 检查 `isLoggedIn()` 函数的返回值

### 问题2: 跳转到错误页面
**原因**: 用户角色判断逻辑有误
**解决**: 检查 `getDefaultLoggedInPath()` 函数

### 问题3: 登录状态丢失
**原因**: localStorage 数据格式不正确
**解决**: 检查 token 和 userInfo 的存储格式

## 📊 测试结果记录

| 测试场景 | 预期结果 | 实际结果 | 状态 |
|---------|---------|---------|------|
| 未登录访问根路径 | 跳转到 /login | | ⏳ |
| 普通用户登录 | 跳转到 /home | | ⏳ |
| 管理员登录 | 跳转到 /home | | ⏳ |
| 已登录访问根路径 | 跳转到对应页面 | | ⏳ |
| 登出功能 | 跳转到 /login | | ⏳ |

---

**测试完成后请更新上表中的实际结果和状态** ✅❌
