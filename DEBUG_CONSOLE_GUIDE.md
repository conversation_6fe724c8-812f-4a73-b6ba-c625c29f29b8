# 🔍 根路径导航调试指南

## 📋 调试信息已添加

我已经在关键节点添加了详细的 console 调试信息，现在您可以清楚地看到导航逻辑的执行过程。

## 🧪 测试步骤

### 1. **打开浏览器开发者工具**
- 按 `F12` 或右键选择"检查"
- 切换到 "Console" 标签页

### 2. **清除浏览器存储**
在控制台执行：
```javascript
localStorage.clear();
sessionStorage.clear();
console.log('✅ 存储已清除');
```

### 3. **访问根路径**
访问: [http://localhost:8000/](http://localhost:8000/)

### 4. **查看调试信息**
您应该在控制台看到以下调试信息：

```
🚀 [RootRedirect] 组件开始挂载
⏰ [RootRedirect] 设置 100ms 延迟执行重定向
⏰ [RootRedirect] 延迟时间到，开始执行重定向
🔄 [RootRedirect] 开始执行重定向逻辑

🔍 [Root Redirect Debug] 详细状态检查
  当前 URL: http://localhost:8000/
  当前路径: /
  Token 检查: {
    front_logix_token: null,
    token: null,
    auth_token: null,
    hasValidToken: false,
    tokenValue: null
  }
  用户信息检查: {
    userInfoStr: null,
    userInfo: null,
    hasValidUserInfo: false
  }
  最终登录状态: false

🔐 [auth.ts] isLoggedIn() 函数被调用
🔑 [auth.ts] getToken() 被调用
🔑 [auth.ts] 检查 front_logix_token: null
🔑 [auth.ts] 检查 token: null
🔑 [auth.ts] 检查 auth_token: null
🔑 [auth.ts] 未找到有效 token

👤 [auth.ts] getUserInfo() 被调用
👤 [auth.ts] 原始用户信息字符串: null
👤 [auth.ts] 未找到用户信息

🔐 [auth.ts] isLoggedIn() 检查结果: {
  token: null,
  hasValidToken: false,
  userInfo: null,
  hasValidUserInfo: false,
  finalResult: false
}

❌ [RootRedirect] 用户未登录，准备跳转到登录页
🎯 [RootRedirect] 跳转命令已执行: history.replace('/login')
```

## 🔍 关键调试点

### 1. **组件挂载检查**
- `🚀 [RootRedirect] 组件开始挂载` - 确认组件被正确加载

### 2. **延迟执行检查**
- `⏰ [RootRedirect] 设置 100ms 延迟执行重定向` - 确认延迟设置
- `⏰ [RootRedirect] 延迟时间到，开始执行重定向` - 确认延迟执行

### 3. **认证状态检查**
- `🔑 [auth.ts] 检查 xxx: null` - 检查各种 token 存储
- `👤 [auth.ts] 原始用户信息字符串: null` - 检查用户信息存储

### 4. **跳转执行检查**
- `🎯 [RootRedirect] 跳转命令已执行: history.replace('/login')` - 确认跳转命令执行

## 🐛 可能的问题

### 问题1: 没有看到任何调试信息
**可能原因**: 
- RootRedirect 组件没有被加载
- 路由配置有问题

**检查方法**: 
- 查看网络面板是否有 404 错误
- 检查 Elements 面板是否有 RootRedirect 组件

### 问题2: 看到调试信息但没有跳转
**可能原因**: 
- history.replace() 没有生效
- 路由被其他逻辑拦截

**检查方法**: 
- 查看是否有 `🎯 [RootRedirect] 跳转命令已执行` 信息
- 检查是否有错误信息

### 问题3: 跳转到错误页面
**可能原因**: 
- 登录状态判断错误
- 默认路径配置错误

**检查方法**: 
- 查看 `最终登录状态` 的值
- 检查 `getDefaultLoggedInPath()` 的返回值

## 📊 测试场景

### 场景1: 未登录用户
1. 清除存储
2. 访问 `/`
3. 预期: 跳转到 `/login`

### 场景2: 已登录用户
1. 在控制台执行模拟登录:
```javascript
localStorage.setItem('front_logix_token', 'test-token');
localStorage.setItem('userInfo', JSON.stringify({
  name: 'Test User',
  username: 'testuser'
}));
```
2. 访问 `/`
3. 预期: 跳转到 `/home`

## 🔧 实时调试

如果您想实时查看存储状态，可以在控制台执行：

```javascript
// 查看当前存储状态
function checkStorage() {
  console.group('📦 当前存储状态');
  console.log('front_logix_token:', localStorage.getItem('front_logix_token'));
  console.log('token:', localStorage.getItem('token'));
  console.log('auth_token:', localStorage.getItem('auth_token'));
  console.log('userInfo:', localStorage.getItem('userInfo'));
  console.groupEnd();
}

checkStorage();
```

---

**服务器地址**: http://localhost:8000  
**状态**: ✅ 运行中  
**调试**: 🔍 已启用
