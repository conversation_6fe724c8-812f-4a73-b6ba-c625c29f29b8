/**
 * 用户 API 单元测试
 */
import api from '../index';
import * as apiClient from '../../utils/apiClient';

// Mock API 客户端
jest.mock('../../utils/apiClient');
const mockedApiClient = apiClient as jest.Mocked<typeof apiClient>;

describe('User API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getUserList', () => {
    it('应该正确调用获取用户列表 API', async () => {
      const mockResponse = {
        success: true,
        data: [
          { id: 1, name: 'User 1' },
          { id: 2, name: 'User 2' },
        ],
      };
      mockedApiClient.get.mockResolvedValue(mockResponse);

      const params = { current: 1, pageSize: 10, keyword: 'test' };
      const result = await api.user.getUserList(params);

      expect(mockedApiClient.get).toHaveBeenCalledWith('/api/v1/queryUserList', params);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('createUser', () => {
    it('应该正确调用创建用户 API', async () => {
      const mockResponse = {
        success: true,
        data: { id: 1, name: 'New User' },
      };
      mockedApiClient.post.mockResolvedValue(mockResponse);

      const userData = { name: 'New User', email: '<EMAIL>' };
      const result = await api.user.createUser(userData);

      expect(mockedApiClient.post).toHaveBeenCalledWith('/api/v1/user', userData, {
        headers: { 'Content-Type': 'application/json' },
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('updateUser', () => {
    it('应该正确调用更新用户 API', async () => {
      const mockResponse = {
        success: true,
        data: { id: 1, name: 'Updated User' },
      };
      mockedApiClient.put.mockResolvedValue(mockResponse);

      const userId = '1';
      const userData = { name: 'Updated User' };
      const result = await api.user.updateUser(userId, userData);

      expect(mockedApiClient.put).toHaveBeenCalledWith(`/api/v1/user/${userId}`, userData, {
        headers: { 'Content-Type': 'application/json' },
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('deleteUser', () => {
    it('应该正确调用删除用户 API', async () => {
      const mockResponse = {
        success: true,
        message: 'User deleted successfully',
      };
      mockedApiClient.del.mockResolvedValue(mockResponse);

      const userId = '1';
      const result = await api.user.deleteUser(userId);

      expect(mockedApiClient.del).toHaveBeenCalledWith(`/api/v1/user/${userId}`, undefined);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('兼容性方法', () => {
    it('queryUserList 应该与 getUserList 行为一致', async () => {
      const mockResponse = { success: true, data: [] };
      mockedApiClient.get.mockResolvedValue(mockResponse);

      const params = { current: 1, pageSize: 10 };

      // 测试两个方法调用相同的底层 API
      await api.user.queryUserList(params);
      await api.user.getUserList(params);

      expect(mockedApiClient.get).toHaveBeenCalledTimes(2);
      expect(mockedApiClient.get).toHaveBeenNthCalledWith(1, '/api/v1/queryUserList', params);
      expect(mockedApiClient.get).toHaveBeenNthCalledWith(2, '/api/v1/queryUserList', params);
    });

    it('addUser 应该与 createUser 行为一致', async () => {
      const mockResponse = { success: true, data: { id: 1 } };
      mockedApiClient.post.mockResolvedValue(mockResponse);

      const userData = { name: 'Test User' };

      await api.user.addUser(userData);
      await api.user.createUser(userData);

      expect(mockedApiClient.post).toHaveBeenCalledTimes(2);
      expect(mockedApiClient.post).toHaveBeenNthCalledWith(1, '/api/v1/user', userData, {
        headers: { 'Content-Type': 'application/json' },
      });
      expect(mockedApiClient.post).toHaveBeenNthCalledWith(2, '/api/v1/user', userData, {
        headers: { 'Content-Type': 'application/json' },
      });
    });
  });
});
