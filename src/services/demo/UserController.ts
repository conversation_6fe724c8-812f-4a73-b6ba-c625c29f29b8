/* eslint-disable */
/**
 * 用户控制器 API
 * 统一使用 axios 客户端
 * 建议使用 @/services/index.ts 中的 userAPI 替代此文件
 */
import { del, get, post, put } from '@/utils/apiClient';
import { ApiResponse } from '@/types/api';

// 统一响应处理
const handleResponse = <T>(response: ApiResponse<T>): T => {
  if (response?.status === 200 || response?.status === 0) {
    return response.data as T;
  }
  throw new Error(response?.message || '请求失败');
};

/** 获取用户列表 GET /api/v1/queryUserList */
export async function queryUserList(
  params: {
    // query
    /** 搜索关键词 */
    keyword?: string;
    /** 当前页码 */
    current?: number;
    /** 每页大小 */
    pageSize?: number;
  },
  options?: { [key: string]: any },
): Promise<any> {
  return get('/api/v1/queryUserList', {
    ...params,
    ...(options || {}),
  }).then(handleResponse);
}

/** 此处后端没有提供注释 POST /api/v1/user */
export async function addUser(
  body?: API.UserInfoVO,
  options?: { [key: string]: any },
) {
  return post('/api/v1/user', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  }).then(handleResponse);
}

/** 此处后端没有提供注释 GET /api/v1/user/${param0} */
export async function getUserDetail(
  params: {
    // path
    /** userId */
    userId?: string;
  },
  options?: { [key: string]: any },
) {
  const { userId: param0 } = params;
  return get(`/api/v1/user/${param0}`, {
    ...params,
    ...(options || {}),
  }).then(handleResponse);
}

/** 此处后端没有提供注释 PUT /api/v1/user/${param0} */
export async function modifyUser(
  params: {
    // path
    /** userId */
    userId?: string;
  },
  body?: API.UserInfoVO,
  options?: { [key: string]: any },
) {
  const { userId: param0 } = params;
  return put(`/api/v1/user/${param0}`, body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...params,
    ...(options || {}),
  }).then(handleResponse);
}

/** 此处后端没有提供注释 DELETE /api/v1/user/${param0} */
export async function deleteUser(
  params: {
    // path
    /** userId */
    userId?: string;
  },
  options?: { [key: string]: any },
) {
  const { userId: param0 } = params;
  return del(`/api/v1/user/${param0}`, {
    ...params,
    ...(options || {}),
  }).then(handleResponse);
}
