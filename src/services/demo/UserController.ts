/* eslint-disable */
// 该文件由 OneAPI 自动生成，请勿手动修改！
import { del, get, post, put } from '@/utils/apiClient';

// 统一响应处理
const handleResponse = <T>(response: any): T => {
  if (response?.code === 200) {
    return response.data;
  }
  throw new Error(response?.message || '请求失败');
};

/** 此处后端没有提供注释 GET /api/v1/queryUserList */
export async function queryUserList(
  params: {
    // query
    /** keyword */
    keyword?: string;
    /** current */
    current?: number;
    /** pageSize */
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  return get('/api/v1/queryUserList', {
    ...params,
    ...(options || {}),
  }).then(handleResponse);
}

/** 此处后端没有提供注释 POST /api/v1/user */
export async function addUser(
  body?: API.UserInfoVO,
  options?: { [key: string]: any },
) {
  return post('/api/v1/user', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  }).then(handleResponse);
}

/** 此处后端没有提供注释 GET /api/v1/user/${param0} */
export async function getUserDetail(
  params: {
    // path
    /** userId */
    userId?: string;
  },
  options?: { [key: string]: any },
) {
  const { userId: param0 } = params;
  return get(`/api/v1/user/${param0}`, {
    ...params,
    ...(options || {}),
  }).then(handleResponse);
}

/** 此处后端没有提供注释 PUT /api/v1/user/${param0} */
export async function modifyUser(
  params: {
    // path
    /** userId */
    userId?: string;
  },
  body?: API.UserInfoVO,
  options?: { [key: string]: any },
) {
  const { userId: param0 } = params;
  return put(`/api/v1/user/${param0}`, body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...params,
    ...(options || {}),
  }).then(handleResponse);
}

/** 此处后端没有提供注释 DELETE /api/v1/user/${param0} */
export async function deleteUser(
  params: {
    // path
    /** userId */
    userId?: string;
  },
  options?: { [key: string]: any },
) {
  const { userId: param0 } = params;
  return del(`/api/v1/user/${param0}`, {
    ...params,
    ...(options || {}),
  }).then(handleResponse);
}
