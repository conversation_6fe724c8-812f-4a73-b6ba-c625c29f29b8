/**
 * 统一的 API 服务层
 * 所有 API 调用都通过这里进行，统一使用 axios
 */
import { get, post, put, del } from '@/utils/apiClient';
import { ApiResponse } from '@/types/api';

// 用户相关 API
export const userAPI = {
  // 获取用户列表
  getUserList: (params: {
    keyword?: string;
    current?: number;
    pageSize?: number;
  }): Promise<ApiResponse<any>> => {
    return get('/api/v1/queryUserList', params);
  },

  // 获取用户详情
  getUserDetail: (id: string): Promise<ApiResponse<any>> => {
    return get(`/api/v1/user/${id}`);
  },

  // 创建用户
  createUser: (data: any): Promise<ApiResponse<any>> => {
    return post('/api/v1/user', data);
  },

  // 更新用户
  updateUser: (id: string, data: any): Promise<ApiResponse<any>> => {
    return put(`/api/v1/user/${id}`, data);
  },

  // 删除用户
  deleteUser: (id: string): Promise<ApiResponse<any>> => {
    return del(`/api/v1/user/${id}`);
  },
};

// 产品相关 API
export const productAPI = {
  // 获取产品列表
  getProductList: (params: {
    keyword?: string;
    current?: number;
    pageSize?: number;
    category?: string;
  }): Promise<ApiResponse<any>> => {
    return get('/api/v1/products', params);
  },

  // 获取产品详情
  getProductDetail: (id: string): Promise<ApiResponse<any>> => {
    return get(`/api/v1/products/${id}`);
  },

  // 创建产品
  createProduct: (data: any): Promise<ApiResponse<any>> => {
    return post('/api/v1/products', data);
  },

  // 更新产品
  updateProduct: (id: string, data: any): Promise<ApiResponse<any>> => {
    return put(`/api/v1/products/${id}`, data);
  },

  // 删除产品
  deleteProduct: (id: string): Promise<ApiResponse<any>> => {
    return del(`/api/v1/products/${id}`);
  },
};

// 冰块订单相关 API
export const iceOrderAPI = {
  // 获取订单列表
  getOrderList: (params: {
    keyword?: string;
    current?: number;
    pageSize?: number;
    status?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<ApiResponse<any>> => {
    return get('/api/v1/ice-orders', params);
  },

  // 获取订单详情
  getOrderDetail: (id: string): Promise<ApiResponse<any>> => {
    return get(`/api/v1/ice-orders/${id}`);
  },

  // 创建订单
  createOrder: (data: any): Promise<ApiResponse<any>> => {
    return post('/api/v1/ice-orders', data);
  },

  // 更新订单
  updateOrder: (id: string, data: any): Promise<ApiResponse<any>> => {
    return put(`/api/v1/ice-orders/${id}`, data);
  },

  // 删除订单
  deleteOrder: (id: string): Promise<ApiResponse<any>> => {
    return del(`/api/v1/ice-orders/${id}`);
  },

  // 获取冰块类型列表
  getIceTypes: (): Promise<ApiResponse<any>> => {
    return get('/api/v1/ice-types');
  },

  // 获取工厂列表
  getFactories: (params?: {
    keyword?: string;
    current?: number;
    pageSize?: number;
  }): Promise<ApiResponse<any>> => {
    return get('/api/v1/factories', params);
  },

  // 获取供应商列表
  getSuppliers: (params?: {
    keyword?: string;
    current?: number;
    pageSize?: number;
  }): Promise<ApiResponse<any>> => {
    return get('/api/v1/suppliers', params);
  },
};

// 监控数据相关 API
export const monitoringAPI = {
  // 获取监控数据报表
  getMonitoringReport: (params: {
    startDate: string;
    endDate: string;
    type?: string;
  }): Promise<ApiResponse<any>> => {
    return get('/api/v1/monitoring/report', params);
  },

  // 获取实时监控数据
  getRealTimeData: (): Promise<ApiResponse<any>> => {
    return get('/api/v1/monitoring/realtime');
  },
};

// 认证相关 API
export const authAPI = {
  // 用户登录
  login: (data: {
    username: string;
    password: string;
  }): Promise<ApiResponse<any>> => {
    return post('/api/v1/auth/login', data);
  },

  // 用户注册
  register: (data: {
    username: string;
    password: string;
    email: string;
  }): Promise<ApiResponse<any>> => {
    return post('/api/v1/auth/register', data);
  },

  // 重置密码
  resetPassword: (data: {
    email: string;
  }): Promise<ApiResponse<any>> => {
    return post('/api/v1/auth/reset-password', data);
  },

  // 获取用户信息
  getUserInfo: (): Promise<ApiResponse<any>> => {
    return get('/api/v1/auth/user-info');
  },

  // 退出登录
  logout: (): Promise<ApiResponse<any>> => {
    return post('/api/v1/auth/logout');
  },
};

// 系统管理相关 API
export const systemAPI = {
  // 获取操作日志
  getOperationLogs: (params: {
    current?: number;
    pageSize?: number;
    startDate?: string;
    endDate?: string;
    module?: string;
    action?: string;
  }): Promise<ApiResponse<any>> => {
    return get('/api/v1/system/operation-logs', params);
  },

  // 获取系统配置
  getSystemConfig: (): Promise<ApiResponse<any>> => {
    return get('/api/v1/system/config');
  },

  // 更新系统配置
  updateSystemConfig: (data: any): Promise<ApiResponse<any>> => {
    return put('/api/v1/system/config', data);
  },
};

// 导出所有 API
export default {
  user: userAPI,
  product: productAPI,
  iceOrder: iceOrderAPI,
  monitoring: monitoringAPI,
  auth: authAPI,
  system: systemAPI,
};
