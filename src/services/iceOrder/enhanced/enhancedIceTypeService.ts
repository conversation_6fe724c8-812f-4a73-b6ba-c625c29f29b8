/**
 * 增强冰块类型服务
 */
import { ApiResponse } from '@/types/api';
import { get, post, put, del } from '@/utils/apiClient';
import { EnhancedIceType, PriceInfo } from '../types/enhanced';

/**
 * 获取增强冰块类型列表
 */
export async function getEnhancedIceTypeList(params?: { [key: string]: any }) {
  return request<ApiResponse<EnhancedIceType[]>>('/api/enhanced-ice-types', {
    method: 'GET',
    params,
  });
}

/**
 * 获取增强冰块类型详情
 */
export async function getEnhancedIceTypeDetail(id: number) {
  return request<ApiResponse<EnhancedIceType>>(
    `/api/enhanced-ice-types/${id}`,
    {
      method: 'GET',
    },
  );
}

/**
 * 创建增强冰块类型
 */
export async function createEnhancedIceType(data: Omit<EnhancedIceType, 'id'>) {
  return request<ApiResponse<EnhancedIceType>>('/api/enhanced-ice-types', {
    method: 'POST',
    data,
  });
}

/**
 * 更新增强冰块类型
 */
export async function updateEnhancedIceType(
  id: number,
  data: Partial<EnhancedIceType>,
) {
  return request<ApiResponse<EnhancedIceType>>(
    `/api/enhanced-ice-types/${id}`,
    {
      method: 'PUT',
      data,
    },
  );
}

/**
 * 删除增强冰块类型
 */
export async function deleteEnhancedIceType(id: number) {
  return request<ApiResponse<any>>(`/api/enhanced-ice-types/${id}`, {
    method: 'DELETE',
  });
}

/**
 * 获取冰块价格信息
 */
export async function getIceTypePrice(id: number) {
  return request<ApiResponse<PriceInfo>>(
    `/api/enhanced-ice-types/${id}/price`,
    {
      method: 'GET',
    },
  );
}

/**
 * 更新冰块价格信息
 */
export async function updateIceTypePrice(id: number, priceInfo: PriceInfo) {
  return request<ApiResponse<PriceInfo>>(
    `/api/enhanced-ice-types/${id}/price`,
    {
      method: 'PUT',
      data: priceInfo,
    },
  );
}

/**
 * 获取冰块历史价格
 */
export async function getIceTypeHistoryPrices(
  id: number,
  params?: { startDate?: string; endDate?: string },
) {
  return request<ApiResponse<PriceInfo['historyPrices']>>(
    `/api/enhanced-ice-types/${id}/history-prices`,
    {
      method: 'GET',
      params,
    },
  );
}

/**
 * 获取冰块3D模型
 */
export async function getIceType3DModel(id: number) {
  return request<ApiResponse<{ modelUrl: string }>>(
    `/api/enhanced-ice-types/${id}/3d-model`,
    {
      method: 'GET',
    },
  );
}

/**
 * 上传冰块3D模型
 */
export async function uploadIceType3DModel(id: number, file: File) {
  const formData = new FormData();
  formData.append('model', file);

  return request<ApiResponse<{ modelUrl: string }>>(
    `/api/enhanced-ice-types/${id}/3d-model`,
    {
      method: 'POST',
      data: formData,
    },
  );
}
