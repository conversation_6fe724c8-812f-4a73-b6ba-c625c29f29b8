/**
 * 工厂信息服务
 * 使用 axios 客户端替代 umi-request
 */
import { get, post, put, del } from '@/utils/apiClient';
import { ApiResponse, Factory } from '../types/enhanced';

/**
 * 获取工厂列表
 */
export async function getFactoryList(params?: { [key: string]: any }) {
  return request<ApiResponse<Factory[]>>('/api/factories', {
    method: 'GET',
    params,
  });
}

/**
 * 获取工厂详情
 */
export async function getFactoryDetail(id: number) {
  return request<ApiResponse<Factory>>(`/api/factories/${id}`, {
    method: 'GET',
  });
}

/**
 * 创建工厂
 */
export async function createFactory(data: Omit<Factory, 'id' | 'createdAt'>) {
  return request<ApiResponse<Factory>>('/api/factories', {
    method: 'POST',
    data,
  });
}

/**
 * 更新工厂信息
 */
export async function updateFactory(id: number, data: Partial<Factory>) {
  return request<ApiResponse<Factory>>(`/api/factories/${id}`, {
    method: 'PUT',
    data,
  });
}

/**
 * 删除工厂
 */
export async function deleteFactory(id: number) {
  return request<ApiResponse<any>>(`/api/factories/${id}`, {
    method: 'DELETE',
  });
}

/**
 * 更新工厂服务区域
 */
export async function updateFactoryServiceArea(
  id: number,
  serviceArea: Factory['serviceArea'],
) {
  return request<ApiResponse<Factory>>(`/api/factories/${id}/service-area`, {
    method: 'PUT',
    data: serviceArea,
  });
}

/**
 * 更新工厂产能信息
 */
export async function updateFactoryCapacity(
  id: number,
  capacity: Factory['capacity'],
) {
  return request<ApiResponse<Factory>>(`/api/factories/${id}/capacity`, {
    method: 'PUT',
    data: capacity,
  });
}

/**
 * 更新生产线状态
 */
export async function updateProductionLineStatus(
  factoryId: number,
  lineId: number,
  status: 'active' | 'maintenance' | 'offline',
) {
  return request<ApiResponse<Factory>>(
    `/api/factories/${factoryId}/production-lines/${lineId}/status`,
    {
      method: 'PUT',
      data: { status },
    },
  );
}
