/**
 * 报告服务
 */
import { ApiResponse } from '@/types/api';
import { get, post, put, del } from '@/utils/apiClient';
import {
  HeatMapData,
  IndustryReport,
  Statistics,
  WeatherData,
} from '../types/enhanced';

/**
 * 获取行业报告列表
 */
export async function getIndustryReportList(params?: { [key: string]: any }) {
  return request<ApiResponse<IndustryReport[]>>('/api/reports/industry', {
    method: 'GET',
    params,
  });
}

/**
 * 获取行业报告详情
 */
export async function getIndustryReportDetail(id: number) {
  return request<ApiResponse<IndustryReport>>(`/api/reports/industry/${id}`, {
    method: 'GET',
  });
}

/**
 * 创建行业报告
 */
export async function createIndustryReport(
  data: Omit<IndustryReport, 'id' | 'publishedAt'>,
) {
  return request<ApiResponse<IndustryReport>>('/api/reports/industry', {
    method: 'POST',
    data,
  });
}

/**
 * 更新行业报告
 */
export async function updateIndustryReport(
  id: number,
  data: Partial<IndustryReport>,
) {
  return request<ApiResponse<IndustryReport>>(`/api/reports/industry/${id}`, {
    method: 'PUT',
    data,
  });
}

/**
 * 删除行业报告
 */
export async function deleteIndustryReport(id: number) {
  return request<ApiResponse<any>>(`/api/reports/industry/${id}`, {
    method: 'DELETE',
  });
}

/**
 * 获取统计数据
 */
export async function getStatistics(params?: {
  startDate?: string;
  endDate?: string;
}) {
  return request<ApiResponse<Statistics>>('/api/reports/statistics', {
    method: 'GET',
    params,
  });
}

/**
 * 获取天气数据
 */
export async function getWeatherData(city: string) {
  return request<ApiResponse<WeatherData>>('/api/reports/weather', {
    method: 'GET',
    params: { city },
  });
}

/**
 * 获取热力图数据
 */
export async function getHeatMapData(region?: string) {
  return request<ApiResponse<HeatMapData[]>>('/api/reports/heat-map', {
    method: 'GET',
    params: { region },
  });
}

/**
 * 获取碳足迹报告
 */
export async function getCarbonFootprintReport(params?: {
  startDate?: string;
  endDate?: string;
}) {
  return request<ApiResponse<any>>('/api/reports/carbon-footprint', {
    method: 'GET',
    params,
  });
}
