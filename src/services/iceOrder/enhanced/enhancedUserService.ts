/**
 * 增强用户服务
 */
import { request } from '@/utils/requestCompat';
import { ApiResponse, EnhancedUser } from '../types/enhanced';

/**
 * 获取增强用户列表
 */
export async function getEnhancedUserList(params?: { [key: string]: any }) {
  return request<ApiResponse<EnhancedUser[]>>('/api/enhanced-users', {
    method: 'GET',
    params,
  });
}

/**
 * 获取增强用户详情
 */
export async function getEnhancedUserDetail(id: number) {
  return request<ApiResponse<EnhancedUser>>(`/api/enhanced-users/${id}`, {
    method: 'GET',
  });
}

/**
 * 创建增强用户
 */
export async function createEnhancedUser(
  data: Omit<EnhancedUser, 'id' | 'createdAt' | 'lastLoginAt'>,
) {
  return request<ApiResponse<EnhancedUser>>('/api/enhanced-users', {
    method: 'POST',
    data,
  });
}

/**
 * 更新增强用户
 */
export async function updateEnhancedUser(
  id: number,
  data: Partial<EnhancedUser>,
) {
  return request<ApiResponse<EnhancedUser>>(`/api/enhanced-users/${id}`, {
    method: 'PUT',
    data,
  });
}

/**
 * 删除增强用户
 */
export async function deleteEnhancedUser(id: number) {
  return request<ApiResponse<any>>(`/api/enhanced-users/${id}`, {
    method: 'DELETE',
  });
}

/**
 * 更新用户信用评分
 */
export async function updateUserCreditScore(id: number, creditScore: number) {
  return request<ApiResponse<EnhancedUser>>(
    `/api/enhanced-users/${id}/credit-score`,
    {
      method: 'PUT',
      data: { creditScore },
    },
  );
}

/**
 * 获取用户订单历史
 */
export async function getUserOrderHistory(id: number) {
  return request<ApiResponse<any[]>>(`/api/enhanced-users/${id}/orders`, {
    method: 'GET',
  });
}

/**
 * 获取用户地址
 */
export async function getUserAddress(id: number) {
  return request<ApiResponse<EnhancedUser['address']>>(
    `/api/enhanced-users/${id}/address`,
    {
      method: 'GET',
    },
  );
}

/**
 * 更新用户地址
 */
export async function updateUserAddress(
  id: number,
  address: EnhancedUser['address'],
) {
  return request<ApiResponse<EnhancedUser>>(
    `/api/enhanced-users/${id}/address`,
    {
      method: 'PUT',
      data: address,
    },
  );
}
