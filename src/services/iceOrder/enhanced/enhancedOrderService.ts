/**
 * 增强订单服务
 */
import { get, post, put, del } from '@/utils/apiClient';
import { OrderStatus } from '../types';
import { ApiResponse, EnhancedOrder } from '../types/enhanced';

/**
 * 获取增强订单列表
 */
export async function getEnhancedOrderList(params?: { [key: string]: any }) {
  return request<ApiResponse<EnhancedOrder[]>>('/api/enhanced-orders', {
    method: 'GET',
    params,
  });
}

/**
 * 获取增强订单详情
 */
export async function getEnhancedOrderDetail(id: number) {
  return request<ApiResponse<EnhancedOrder>>(`/api/enhanced-orders/${id}`, {
    method: 'GET',
  });
}

/**
 * 创建增强订单
 */
export async function createEnhancedOrder(
  data: Omit<EnhancedOrder, 'id' | 'orderNumber' | 'createdAt' | 'updatedAt'>,
) {
  return request<ApiResponse<EnhancedOrder>>('/api/enhanced-orders', {
    method: 'POST',
    data,
  });
}

/**
 * 更新增强订单
 */
export async function updateEnhancedOrder(
  id: number,
  data: Partial<EnhancedOrder>,
) {
  return request<ApiResponse<EnhancedOrder>>(`/api/enhanced-orders/${id}`, {
    method: 'PUT',
    data,
  });
}

/**
 * 更新订单状态
 */
export async function updateEnhancedOrderStatus(
  id: number,
  status: OrderStatus,
) {
  return request<ApiResponse<EnhancedOrder>>(
    `/api/enhanced-orders/${id}/status`,
    {
      method: 'PUT',
      data: { status },
    },
  );
}

/**
 * 更新订单支付状态
 */
export async function updateOrderPaymentStatus(
  id: number,
  paymentStatus: 'unpaid' | 'paid' | 'refunded',
  paymentMethod?: 'alipay' | 'wechat' | 'bank-transfer' | 'cash',
) {
  return request<ApiResponse<EnhancedOrder>>(
    `/api/enhanced-orders/${id}/payment`,
    {
      method: 'PUT',
      data: { paymentStatus, paymentMethod },
    },
  );
}

/**
 * 更新物流信息
 */
export async function updateOrderLogistics(
  id: number,
  logisticsInfo: EnhancedOrder['logisticsInfo'],
) {
  return request<ApiResponse<EnhancedOrder>>(
    `/api/enhanced-orders/${id}/logistics`,
    {
      method: 'PUT',
      data: logisticsInfo,
    },
  );
}

/**
 * 添加保险信息
 */
export async function addOrderInsurance(
  id: number,
  insurance: EnhancedOrder['insurance'],
) {
  return request<ApiResponse<EnhancedOrder>>(
    `/api/enhanced-orders/${id}/insurance`,
    {
      method: 'PUT',
      data: insurance,
    },
  );
}

/**
 * 生成电子合同
 */
export async function generateOrderContract(id: number) {
  return request<ApiResponse<EnhancedOrder['contract']>>(
    `/api/enhanced-orders/${id}/contract`,
    {
      method: 'POST',
    },
  );
}

/**
 * 计算订单碳足迹
 */
export async function calculateOrderCarbonFootprint(id: number) {
  return request<ApiResponse<EnhancedOrder['carbonFootprint']>>(
    `/api/enhanced-orders/${id}/carbon-footprint`,
    {
      method: 'GET',
    },
  );
}

/**
 * 获取订单统计数据
 */
export async function getOrderStatistics(params?: {
  startDate?: string;
  endDate?: string;
}) {
  return request<ApiResponse<any>>('/api/enhanced-orders/statistics', {
    method: 'GET',
    params,
  });
}
