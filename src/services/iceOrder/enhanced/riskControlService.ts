/**
 * 风控服务
 */
import { request } from '@/utils/requestCompat';
import { ApiResponse, RiskAlert, SystemNotification } from '../types/enhanced';

/**
 * 获取风控预警列表
 */
export async function getRiskAlertList(params?: { [key: string]: any }) {
  return request<ApiResponse<RiskAlert[]>>('/api/risk-control/alerts', {
    method: 'GET',
    params,
  });
}

/**
 * 获取风控预警详情
 */
export async function getRiskAlertDetail(id: number) {
  return request<ApiResponse<RiskAlert>>(`/api/risk-control/alerts/${id}`, {
    method: 'GET',
  });
}

/**
 * 创建风控预警
 */
export async function createRiskAlert(
  data: Omit<RiskAlert, 'id' | 'createdAt' | 'resolvedAt'>,
) {
  return request<ApiResponse<RiskAlert>>('/api/risk-control/alerts', {
    method: 'POST',
    data,
  });
}

/**
 * 更新风控预警
 */
export async function updateRiskAlert(id: number, data: Partial<RiskAlert>) {
  return request<ApiResponse<RiskAlert>>(`/api/risk-control/alerts/${id}`, {
    method: 'PUT',
    data,
  });
}

/**
 * 解决风控预警
 */
export async function resolveRiskAlert(id: number) {
  return request<ApiResponse<RiskAlert>>(
    `/api/risk-control/alerts/${id}/resolve`,
    {
      method: 'PUT',
    },
  );
}

/**
 * 获取系统通知列表
 */
export async function getSystemNotificationList(params?: {
  [key: string]: any;
}) {
  return request<ApiResponse<SystemNotification[]>>(
    '/api/risk-control/notifications',
    {
      method: 'GET',
      params,
    },
  );
}

/**
 * 创建系统通知
 */
export async function createSystemNotification(
  data: Omit<SystemNotification, 'id' | 'createdAt' | 'read'>,
) {
  return request<ApiResponse<SystemNotification>>(
    '/api/risk-control/notifications',
    {
      method: 'POST',
      data,
    },
  );
}

/**
 * 标记通知为已读
 */
export async function markNotificationAsRead(id: number) {
  return request<ApiResponse<SystemNotification>>(
    `/api/risk-control/notifications/${id}/read`,
    {
      method: 'PUT',
    },
  );
}

/**
 * 检测价格异常
 */
export async function detectPriceAnomaly(params?: {
  region?: string;
  productId?: number;
}) {
  return request<ApiResponse<any>>('/api/risk-control/detect-price-anomaly', {
    method: 'GET',
    params,
  });
}

/**
 * 检测区域垄断风险
 */
export async function detectMonopolyRisk(region: string) {
  return request<ApiResponse<any>>('/api/risk-control/detect-monopoly-risk', {
    method: 'GET',
    params: { region },
  });
}

/**
 * 检测恶意竞价
 */
export async function detectMaliciousBidding(supplierId?: number) {
  return request<ApiResponse<any>>(
    '/api/risk-control/detect-malicious-bidding',
    {
      method: 'GET',
      params: { supplierId },
    },
  );
}
