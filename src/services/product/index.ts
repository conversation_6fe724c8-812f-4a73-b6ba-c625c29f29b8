/* eslint-disable */
// 产品管理服务
// 使用 axios 客户端替代 umi-request

import { get, post, put, del } from '@/utils/apiClient';

// 产品图片类型
export interface ProductImage {
  id: number;
  src: string;
  alt: string;
}

// 产品信息类型
export interface Product {
  id: number;
  title: string;
  description: string;
  features: string[];
  applications: string[];
  images: ProductImage[];
  specifications: Record<string, string>;
}

// 产品列表响应类型
export interface ProductListResponse {
  status: number;
  message: string;
  data: Product[];
}

// 产品详情响应类型
export interface ProductDetailResponse {
  status: number;
  message: string;
  data: Product;
}

/**
 * 获取产品列表
 */
export async function getProductList(options?: { [key: string]: any }): Promise<ProductListResponse> {
  return get('/api/products', options);
}

/**
 * 获取产品详情
 */
export async function getProductDetail(id: number, options?: { [key: string]: any }): Promise<ProductDetailResponse> {
  return get(`/api/products/${id}`, options);
}

/**
 * 创建产品
 */
export async function createProduct(data: Omit<Product, 'id'>, options?: { [key: string]: any }): Promise<ProductDetailResponse> {
  return post('/api/products', data);
}

/**
 * 更新产品
 */
export async function updateProduct(id: number, data: Partial<Product>, options?: { [key: string]: any }): Promise<ProductDetailResponse> {
  return put(`/api/products/${id}`, data);
}

/**
 * 删除产品
 */
export async function deleteProduct(id: number, options?: { [key: string]: any }): Promise<{ status: number; message: string }> {
  return del(`/api/products/${id}`, options);
}

export default {
  getProductList,
  getProductDetail,
  createProduct,
  updateProduct,
  deleteProduct,
};
