/**
 * 全局认证提供者组件
 * 负责管理整个应用的登录状态和会话管理
 */
import { isLoggedIn, updateLastActivity, shouldRefreshToken } from '@/utils/auth';
import { message } from 'antd';
import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useNavigate } from 'umi';

// 认证上下文类型定义
interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  checkAuth: () => boolean;
  refreshAuth: () => void;
}

// 创建认证上下文
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// 认证提供者属性
interface AuthProviderProps {
  children: React.ReactNode;
}

// 认证检查间隔（毫秒）- 每30秒检查一次
const AUTH_CHECK_INTERVAL = 30 * 1000;

// 活动监听事件列表
const ACTIVITY_EVENTS = [
  'mousedown',
  'mousemove',
  'keypress',
  'scroll',
  'touchstart',
  'click',
];

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const navigate = useNavigate();

  // 检查认证状态
  const checkAuth = useCallback((): boolean => {
    console.log('🔍 [AuthProvider] 检查认证状态');

    try {
      const authStatus = isLoggedIn();
      setIsAuthenticated(authStatus);

      if (!authStatus && window.location.pathname !== '/login') {
        console.log('🚫 [AuthProvider] 用户未认证，跳转到登录页');
        message.warning('登录已过期，请重新登录');
        navigate('/login', { replace: true });
      }

      return authStatus;
    } catch (error) {
      console.error('❌ [AuthProvider] 认证检查失败:', error);
      setIsAuthenticated(false);
      return false;
    }
  }, [navigate]);

  // 刷新认证状态
  const refreshAuth = useCallback(() => {
    console.log('🔄 [AuthProvider] 刷新认证状态');
    checkAuth();
  }, [checkAuth]);

  // 处理用户活动
  const handleUserActivity = useCallback(() => {
    if (isAuthenticated) {
      updateLastActivity();

      // 检查是否需要刷新 token
      if (shouldRefreshToken()) {
        console.log('🔄 [AuthProvider] 需要刷新 token');
        // 这里可以调用 API 刷新 token
        // refreshToken();
      }
    }
  }, [isAuthenticated]);

  // 初始化认证状态
  useEffect(() => {
    console.log('🚀 [AuthProvider] 初始化认证状态');

    const initAuth = async () => {
      setIsLoading(true);

      // 延迟一点时间，确保其他组件准备就绪
      await new Promise(resolve => setTimeout(resolve, 100));

      checkAuth();
      setIsLoading(false);
    };

    initAuth();
  }, [checkAuth]);

  // 设置定期认证检查
  useEffect(() => {
    console.log('⏰ [AuthProvider] 设置定期认证检查');

    const interval = setInterval(() => {
      console.log('⏰ [AuthProvider] 执行定期认证检查');
      checkAuth();
    }, AUTH_CHECK_INTERVAL);

    return () => {
      console.log('🧹 [AuthProvider] 清理定期认证检查');
      clearInterval(interval);
    };
  }, [checkAuth]);

  // 设置用户活动监听
  useEffect(() => {
    console.log('👂 [AuthProvider] 设置用户活动监听');

    ACTIVITY_EVENTS.forEach(event => {
      document.addEventListener(event, handleUserActivity, true);
    });

    return () => {
      console.log('🧹 [AuthProvider] 清理用户活动监听');
      ACTIVITY_EVENTS.forEach(event => {
        document.removeEventListener(event, handleUserActivity, true);
      });
    };
  }, [handleUserActivity]);

  // 监听路由变化
  useEffect(() => {
    console.log('🛣️ [AuthProvider] 路由变化，检查认证状态');

    // 如果不是登录页面，检查认证状态
    if (window.location.pathname !== '/login') {
      checkAuth();
    }
  }, [checkAuth]);

  const contextValue: AuthContextType = {
    isAuthenticated,
    isLoading,
    checkAuth,
    refreshAuth,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// 使用认证上下文的 Hook
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthProvider;
