import { Avatar, Dropdown } from 'antd';
import { useNavigate } from 'umi';
import styles from './index.less';

export default function Header() {
  const navigate = useNavigate();

  return (
    <div className={styles.header}>
      <div className={styles.logo}>冰品管理系统</div>
      <div className={styles.right}>
        <Dropdown
          menu={{
            items: [
              {
                key: 'profile',
                label: '个人信息',
                onClick: () => navigate('/profile'),
              },
              {
                key: 'logout',
                label: '退出登录',
                onClick: () => {
                  localStorage.removeItem('token');
                  navigate('/login');
                },
              },
            ],
          }}
          placement="bottomRight"
        >
          <div className={styles.profileBtn}>
            <Avatar />
            <span style={{ marginLeft: 8 }}>个人信息</span>
          </div>
        </Dropdown>
      </div>
    </div>
  );
}
