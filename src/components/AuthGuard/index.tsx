/**
 * 认证守卫组件
 * 用于保护需要登录才能访问的页面
 */
import { isLoggedIn } from '@/utils/auth';
import { Spin } from 'antd';
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'umi';

interface AuthGuardProps {
  children: React.ReactNode;
  /**
   * 是否需要登录
   * @default true
   */
  requireAuth?: boolean;
  /**
   * 未登录时的重定向路径
   * @default '/login'
   */
  redirectTo?: string;
  /**
   * 检查权限的函数
   */
  checkPermission?: () => boolean;
  /**
   * 权限不足时的重定向路径
   * @default '/403'
   */
  noPermissionRedirectTo?: string;
}

const AuthGuard: React.FC<AuthGuardProps> = ({
  children,
  requireAuth = true,
  redirectTo = '/login',
  checkPermission,
  noPermissionRedirectTo = '/403',
}) => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [hasAccess, setHasAccess] = useState(false);

  useEffect(() => {
    const checkAccess = () => {
      // 如果不需要认证，直接允许访问
      if (!requireAuth) {
        setHasAccess(true);
        setLoading(false);
        return;
      }

      // 检查登录状态
      const loggedIn = isLoggedIn();

      if (!loggedIn) {
        console.log('用户未登录，重定向到登录页');
        navigate(redirectTo, { replace: true });
        return;
      }

      // 检查权限
      if (checkPermission && !checkPermission()) {
        console.log('用户权限不足，重定向到权限不足页面');
        navigate(noPermissionRedirectTo, { replace: true });
        return;
      }

      // 通过所有检查
      setHasAccess(true);
      setLoading(false);
    };

    // 延迟检查，确保组件完全挂载
    const timer = setTimeout(checkAccess, 50);

    return () => clearTimeout(timer);
  }, [requireAuth, redirectTo, checkPermission, noPermissionRedirectTo]);

  // 显示加载状态
  if (loading) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '200px',
        }}
      >
        <Spin size="large" />
      </div>
    );
  }

  // 有访问权限时渲染子组件
  if (hasAccess) {
    return <>{children}</>;
  }

  // 其他情况返回空（通常不会到达这里，因为会重定向）
  return null;
};

export default AuthGuard;
