/**
 * 路由守卫组件
 * 保护需要认证的路由，确保只有登录用户才能访问
 */
import { useAuth } from '@/components/AuthProvider';
import { isLoggedIn } from '@/utils/auth';
import { history } from '@umijs/max';
import { Spin } from 'antd';
import React, { useEffect, useState } from 'react';

interface RouteGuardProps {
  children: React.ReactNode;
  /**
   * 是否需要认证
   * @default true
   */
  requireAuth?: boolean;
  /**
   * 未认证时的重定向路径
   * @default '/login'
   */
  redirectTo?: string;
  /**
   * 自定义权限检查函数
   */
  checkPermission?: () => boolean;
  /**
   * 权限不足时的重定向路径
   * @default '/403'
   */
  noPermissionRedirectTo?: string;
}

const RouteGuard: React.FC<RouteGuardProps> = ({
  children,
  requireAuth = true,
  redirectTo = '/login',
  checkPermission,
  noPermissionRedirectTo = '/403',
}) => {
  const { isAuthenticated, isLoading } = useAuth();
  const [hasAccess, setHasAccess] = useState<boolean>(false);
  const [checking, setChecking] = useState<boolean>(true);

  useEffect(() => {
    console.log('🛡️ [RouteGuard] 开始路由守卫检查');
    
    const performCheck = async () => {
      setChecking(true);

      try {
        // 如果不需要认证，直接允许访问
        if (!requireAuth) {
          console.log('🛡️ [RouteGuard] 路由不需要认证，允许访问');
          setHasAccess(true);
          setChecking(false);
          return;
        }

        // 等待认证状态加载完成
        if (isLoading) {
          console.log('🛡️ [RouteGuard] 等待认证状态加载...');
          return;
        }

        // 检查基础认证状态
        const authStatus = isAuthenticated || isLoggedIn();
        
        if (!authStatus) {
          console.log('🛡️ [RouteGuard] 用户未认证，重定向到登录页');
          history.replace(redirectTo);
          setChecking(false);
          return;
        }

        // 检查自定义权限
        if (checkPermission && !checkPermission()) {
          console.log('🛡️ [RouteGuard] 用户权限不足，重定向到权限不足页面');
          history.replace(noPermissionRedirectTo);
          setChecking(false);
          return;
        }

        // 通过所有检查
        console.log('🛡️ [RouteGuard] 路由守卫检查通过，允许访问');
        setHasAccess(true);
        setChecking(false);
      } catch (error) {
        console.error('❌ [RouteGuard] 路由守卫检查失败:', error);
        history.replace(redirectTo);
        setChecking(false);
      }
    };

    performCheck();
  }, [
    requireAuth,
    isAuthenticated,
    isLoading,
    redirectTo,
    checkPermission,
    noPermissionRedirectTo,
  ]);

  // 显示加载状态
  if (checking || isLoading) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          flexDirection: 'column',
        }}
      >
        <Spin size="large" />
        <div style={{ marginTop: 16, color: '#666' }}>
          验证登录状态...
        </div>
      </div>
    );
  }

  // 有访问权限时渲染子组件
  if (hasAccess) {
    return <>{children}</>;
  }

  // 其他情况返回空（通常不会到达这里，因为会重定向）
  return null;
};

export default RouteGuard;
