/**
 * 调试测试页面
 * 用于验证路由和组件是否正常工作
 */
import React, { useEffect } from 'react';
import { Card, Button, Space } from 'antd';
import { history } from '@umijs/max';

// 全局调试信息
console.log('📁 [DebugTest] 文件被加载');

const DebugTest: React.FC = () => {
  console.log('🚀 [DebugTest] 组件开始渲染');

  useEffect(() => {
    console.log('🚀 [DebugTest] 组件已挂载');
    console.log('当前 URL:', window.location.href);
    console.log('当前路径:', window.location.pathname);
  }, []);

  const handleGoToRoot = () => {
    console.log('🔄 [DebugTest] 准备跳转到根路径');
    history.push('/');
  };

  const handleGoToLogin = () => {
    console.log('🔄 [DebugTest] 准备跳转到登录页');
    history.push('/login');
  };

  const handleGoToHome = () => {
    console.log('🔄 [DebugTest] 准备跳转到首页');
    history.push('/home');
  };

  return (
    <div style={{ padding: 24, maxWidth: 800, margin: '0 auto' }}>
      <Card title="🔍 调试测试页面" style={{ marginBottom: 16 }}>
        <p>这个页面用于测试路由和组件是否正常工作。</p>
        <p><strong>当前 URL</strong>: {window.location.href}</p>
        <p><strong>当前路径</strong>: {window.location.pathname}</p>
      </Card>

      <Card title="🧪 路由测试" style={{ marginBottom: 16 }}>
        <Space wrap>
          <Button type="primary" onClick={handleGoToRoot}>
            跳转到根路径 (/)
          </Button>
          <Button onClick={handleGoToLogin}>
            跳转到登录页 (/login)
          </Button>
          <Button onClick={handleGoToHome}>
            跳转到首页 (/home)
          </Button>
        </Space>
      </Card>

      <Card title="📋 测试说明">
        <ul>
          <li>如果您能看到这个页面，说明路由系统正常工作</li>
          <li>点击上面的按钮测试路由跳转</li>
          <li>查看浏览器控制台的调试信息</li>
          <li>如果根路径跳转有问题，我们可以进一步排查</li>
        </ul>
      </Card>
    </div>
  );
};

export default DebugTest;
