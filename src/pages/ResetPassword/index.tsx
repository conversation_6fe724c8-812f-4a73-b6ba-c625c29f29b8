import React from 'react';
import { useNavigate } from 'umi';
import './reset-password.less';

const ResetPassword: React.FC = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = React.useState({
    email: '',
    verificationCode: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [countdown, setCountdown] = React.useState(0);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // 密码重置逻辑
    navigate('/login');
  };

  const sendVerificationCode = () => {
    if (!formData.email) return;

    // 发送验证码逻辑
    setCountdown(60);
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) clearInterval(timer);
        return prev - 1;
      });
    }, 1000);
  };

  return (
    <div className="reset-password-container">
      <h2>找回密码</h2>
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label>邮箱</label>
          <input
            type="email"
            value={formData.email}
            onChange={(e) =>
              setFormData({ ...formData, email: e.target.value })
            }
            required
          />
        </div>
        <div className="form-group verification-code">
          <label>验证码</label>
          <input
            type="text"
            value={formData.verificationCode}
            onChange={(e) =>
              setFormData({ ...formData, verificationCode: e.target.value })
            }
            required
          />
          <button
            type="button"
            onClick={sendVerificationCode}
            disabled={countdown > 0}
          >
            {countdown > 0 ? `${countdown}s后重试` : '获取验证码'}
          </button>
        </div>
        <div className="form-group">
          <label>新密码</label>
          <input
            type="password"
            value={formData.newPassword}
            onChange={(e) =>
              setFormData({ ...formData, newPassword: e.target.value })
            }
            required
          />
        </div>
        <div className="form-group">
          <label>确认密码</label>
          <input
            type="password"
            value={formData.confirmPassword}
            onChange={(e) =>
              setFormData({ ...formData, confirmPassword: e.target.value })
            }
            required
          />
        </div>
        <button type="submit" className="reset-button">
          重置密码
        </button>
      </form>
      <div className="back-to-login">
        <a onClick={() => navigate('/login')}>返回登录</a>
      </div>
    </div>
  );
};

export default ResetPassword;
