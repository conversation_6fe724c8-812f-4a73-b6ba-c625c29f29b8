import React, { useEffect } from 'react';
import { history } from '@umijs/max';
import './login.less';

const Login: React.FC = () => {

  useEffect(() => {
    console.group('[Login Component Debug]');
    console.log('Current route:', window.location.pathname);
    console.log('Auth token exists:', !!localStorage.getItem('token'));
    console.groupEnd();
  }, []);
  const [username, setUsername] = React.useState('');
  const [password, setPassword] = React.useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // 设置token
    localStorage.setItem('token', 'dummy-token');

    // 根据用户名设置权限
    const userInfo = {
      name: username,
      isAdmin: username === 'frost-chain-admin',
      roles: username === 'frost-chain-admin' ? ['admin'] : ['user'],
      userType: username === 'frost-chain-admin' ? 'admin' : 'user',
    };

    localStorage.setItem('userInfo', JSON.stringify(userInfo));

    console.log('登录成功，用户权限已设置:', userInfo);
    history.push('/');
  };

  return (
    <div className="login-container">
      <h2>用户登录</h2>
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label>用户名</label>
          <input
            type="text"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            required
          />
        </div>
        <div className="form-group">
          <label>密码</label>
          <input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
          />
        </div>
        <button type="submit" className="login-button">
          登录
        </button>
      </form>
      <div className="auth-links">
        <a onClick={() => history.push('/register')}>注册账号</a>
        <span className="divider">|</span>
        <a onClick={() => history.push('/reset-password')}>忘记密码</a>
      </div>
    </div>
  );
};

export default Login;
