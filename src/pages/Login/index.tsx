import { isLoggedIn, setToken, setUserInfo, setRefreshToken } from '@/utils/auth';
import { message } from 'antd';
import React, { useEffect } from 'react';
import { useNavigate } from 'umi';
import './login.less';

const Login: React.FC = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // 如果用户已经登录，直接跳转到首页
    if (isLoggedIn()) {
      console.log('用户已登录，跳转到首页');
      navigate('/home', { replace: true });
      return;
    }

    console.group('[Login Component Debug]');
    console.log('Current route:', window.location.pathname);
    console.log('User logged in:', isLoggedIn());
    console.groupEnd();
  }, [navigate]);

  const [username, setUsername] = React.useState('');
  const [password, setPassword] = React.useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // 模拟登录验证
      if (!username.trim()) {
        message.error('请输入用户名');
        return;
      }

      // 设置 token 和刷新 token
      const token = `token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const refreshToken = `refresh_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      setToken(token);
      setRefreshToken(refreshToken);

      // 根据用户名设置权限
      const userInfo = {
        id: Date.now().toString(),
        name: username,
        username: username,
        email: `${username}@example.com`,
        isAdmin: username === 'frost-chain-admin',
        roles: username === 'frost-chain-admin' ? ['admin'] : ['user'],
        userType: username === 'frost-chain-admin' ? 'admin' : 'user',
        loginTime: Date.now(),
      };

      setUserInfo(userInfo);

      console.log('✅ 登录成功，用户权限已设置:', userInfo);
      message.success(`欢迎回来，${userInfo.name}！`);

      // 登录成功后跳转到根路径，让 RootRedirect 组件处理具体跳转逻辑
      navigate('/');
    } catch (error) {
      console.error('❌ 登录失败:', error);
      message.error('登录失败，请重试');
    }
  };

  return (
    <div className="login-container">
      <h2>用户登录</h2>
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label>用户名</label>
          <input
            type="text"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            required
          />
        </div>
        <div className="form-group">
          <label>密码</label>
          <input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
          />
        </div>
        <button type="submit" className="login-button">
          登录
        </button>
      </form>
      <div className="auth-links">
        <a onClick={() => history.push('/register')}>注册账号</a>
        <span className="divider">|</span>
        <a onClick={() => history.push('/reset-password')}>忘记密码</a>
      </div>
    </div>
  );
};

export default Login;
