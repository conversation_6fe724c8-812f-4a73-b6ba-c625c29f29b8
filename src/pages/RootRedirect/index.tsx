/**
 * 根路径重定向组件
 * 根据用户登录状态智能跳转到对应页面
 */
import { useAuth } from '@/components/AuthProvider';
import { getUserInfo, hasRole, isAdmin, isLoggedIn } from '@/utils/auth';
import { history } from '@umijs/max';
import { Spin } from 'antd';
import React, { useEffect } from 'react';

// 全局调试信息 - 确认文件被加载
console.log('📁 [RootRedirect] 文件被加载');

/**
 * 获取登录后的默认跳转路径
 * 根据用户角色或权限返回不同的默认页面
 */
const getDefaultLoggedInPath = (): string => {
  try {
    const userInfo = getUserInfo();
    if (!userInfo) {
      return '/home';
    }

    // 根据用户类型或角色决定默认页面
    if (isAdmin()) {
      return '/home'; // 管理员默认到首页
    }

    if (hasRole('operator')) {
      return '/ice-order/dashboard'; // 操作员默认到数据看板
    }

    if (hasRole('viewer')) {
      return '/monitoring-report'; // 查看者默认到监控报表
    }

    // 根据用户类型决定
    if (userInfo.userType === 'supplier') {
      return '/ice-order/suppliers'; // 供应商用户默认到供应商管理
    }

    if (userInfo.userType === 'factory') {
      return '/ice-order/factories'; // 工厂用户默认到工厂管理
    }
  } catch (error) {
    console.warn('解析用户信息失败，使用默认路径:', error);
  }

  // 默认跳转到首页
  return '/home';
};

const RootRedirect: React.FC = () => {
  // 立即输出调试信息，不等待 useEffect
  console.log('🚀 [RootRedirect] 组件开始渲染');

  // 使用认证上下文
  const { isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    console.log('🚀 [RootRedirect] 组件开始挂载');

    const performRedirect = () => {
      console.log('🔄 [RootRedirect] 开始执行重定向逻辑');

      // 等待认证状态加载完成
      if (isLoading) {
        console.log('⏳ [RootRedirect] 等待认证状态加载...');
        return;
      }

      // 使用认证上下文的状态，同时也检查本地状态
      const authFromContext = isAuthenticated;
      const authFromLocal = isLoggedIn();
      const userLoggedIn = authFromContext || authFromLocal;

      // 详细的调试信息
      console.group('🔍 [Root Redirect Debug] 详细状态检查');
      console.log('当前 URL:', window.location.href);
      console.log('当前路径:', window.location.pathname);
      console.log('认证状态:', {
        'authFromContext': authFromContext,
        'authFromLocal': authFromLocal,
        'finalResult': userLoggedIn,
        'isLoading': isLoading
      });
      console.groupEnd();

      if (userLoggedIn) {
        // 已登录，跳转到默认页面
        const defaultPath = getDefaultLoggedInPath();
        console.log(`✅ [RootRedirect] 用户已登录，准备跳转到: ${defaultPath}`);

        try {
          history.replace(defaultPath);
          console.log(`🎯 [RootRedirect] 跳转命令已执行: history.replace('${defaultPath}')`);
        } catch (error) {
          console.error('❌ [RootRedirect] 跳转失败:', error);
        }
      } else {
        // 未登录，跳转到登录页
        console.log('❌ [RootRedirect] 用户未登录，准备跳转到登录页');

        try {
          history.replace('/login');
          console.log('🎯 [RootRedirect] 跳转命令已执行: history.replace(\'/login\')');
        } catch (error) {
          console.error('❌ [RootRedirect] 跳转失败:', error);
        }
      }
    };

    // 使用 setTimeout 确保在组件挂载后执行重定向
    // 避免在 React 渲染过程中进行路由跳转
    console.log('⏰ [RootRedirect] 设置 100ms 延迟执行重定向');
    const timer = setTimeout(() => {
      console.log('⏰ [RootRedirect] 延迟时间到，开始执行重定向');
      performRedirect();
    }, 100);

    return () => {
      console.log('🧹 [RootRedirect] 组件卸载，清理定时器');
      clearTimeout(timer);
    };
  }, [isAuthenticated, isLoading]);

  // 显示加载状态，避免页面闪烁
  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column',
      }}
    >
      <Spin size="large" />
      <div style={{ marginTop: 16, color: '#666' }}>正在跳转...</div>
    </div>
  );
};

export default RootRedirect;
