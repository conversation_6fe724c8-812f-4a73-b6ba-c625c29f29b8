/**
 * 根路径重定向组件
 * 根据用户登录状态智能跳转到对应页面
 */
import { getUserInfo, hasRole, isAdmin, isLoggedIn } from '@/utils/auth';
import { history } from '@umijs/max';
import { Spin } from 'antd';
import React, { useEffect } from 'react';

/**
 * 获取登录后的默认跳转路径
 * 根据用户角色或权限返回不同的默认页面
 */
const getDefaultLoggedInPath = (): string => {
  try {
    const userInfo = getUserInfo();
    if (!userInfo) {
      return '/home';
    }

    // 根据用户类型或角色决定默认页面
    if (isAdmin()) {
      return '/home'; // 管理员默认到首页
    }

    if (hasRole('operator')) {
      return '/ice-order/dashboard'; // 操作员默认到数据看板
    }

    if (hasRole('viewer')) {
      return '/monitoring-report'; // 查看者默认到监控报表
    }

    // 根据用户类型决定
    if (userInfo.userType === 'supplier') {
      return '/ice-order/suppliers'; // 供应商用户默认到供应商管理
    }

    if (userInfo.userType === 'factory') {
      return '/ice-order/factories'; // 工厂用户默认到工厂管理
    }
  } catch (error) {
    console.warn('解析用户信息失败，使用默认路径:', error);
  }

  // 默认跳转到首页
  return '/home';
};

const RootRedirect: React.FC = () => {
  useEffect(() => {
    console.log('🚀 [RootRedirect] 组件开始挂载');

    const performRedirect = () => {
      console.log('🔄 [RootRedirect] 开始执行重定向逻辑');

      // 详细的认证状态检查
      const token = localStorage.getItem('front_logix_token') ||
                   localStorage.getItem('token') ||
                   localStorage.getItem('auth_token');
      const userInfoStr = localStorage.getItem('userInfo');
      let userInfo = null;

      try {
        if (userInfoStr) {
          userInfo = JSON.parse(userInfoStr);
        }
      } catch (e) {
        console.error('❌ [RootRedirect] 解析用户信息失败:', e);
      }

      const hasValidToken = !!(token && token.trim() !== '');
      const hasValidUserInfo = !!(userInfo && (userInfo.name || userInfo.id || userInfo.username));
      const userLoggedIn = hasValidToken && hasValidUserInfo;

      // 详细的调试信息
      console.group('🔍 [Root Redirect Debug] 详细状态检查');
      console.log('当前 URL:', window.location.href);
      console.log('当前路径:', window.location.pathname);
      console.log('Token 检查:', {
        'front_logix_token': localStorage.getItem('front_logix_token'),
        'token': localStorage.getItem('token'),
        'auth_token': localStorage.getItem('auth_token'),
        'hasValidToken': hasValidToken,
        'tokenValue': token ? `${token.substring(0, 10)}...` : null
      });
      console.log('用户信息检查:', {
        'userInfoStr': userInfoStr,
        'userInfo': userInfo,
        'hasValidUserInfo': hasValidUserInfo
      });
      console.log('最终登录状态:', userLoggedIn);
      console.log('isLoggedIn() 函数返回:', isLoggedIn());
      console.groupEnd();

      if (userLoggedIn) {
        // 已登录，跳转到默认页面
        const defaultPath = getDefaultLoggedInPath();
        console.log(`✅ [RootRedirect] 用户已登录，准备跳转到: ${defaultPath}`);

        try {
          history.replace(defaultPath);
          console.log(`🎯 [RootRedirect] 跳转命令已执行: history.replace('${defaultPath}')`);
        } catch (error) {
          console.error('❌ [RootRedirect] 跳转失败:', error);
        }
      } else {
        // 未登录，跳转到登录页
        console.log('❌ [RootRedirect] 用户未登录，准备跳转到登录页');

        try {
          history.replace('/login');
          console.log('🎯 [RootRedirect] 跳转命令已执行: history.replace(\'/login\')');
        } catch (error) {
          console.error('❌ [RootRedirect] 跳转失败:', error);
        }
      }
    };

    // 使用 setTimeout 确保在组件挂载后执行重定向
    // 避免在 React 渲染过程中进行路由跳转
    console.log('⏰ [RootRedirect] 设置 100ms 延迟执行重定向');
    const timer = setTimeout(() => {
      console.log('⏰ [RootRedirect] 延迟时间到，开始执行重定向');
      performRedirect();
    }, 100);

    return () => {
      console.log('🧹 [RootRedirect] 组件卸载，清理定时器');
      clearTimeout(timer);
    };
  }, []);

  // 显示加载状态，避免页面闪烁
  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column',
      }}
    >
      <Spin size="large" />
      <div style={{ marginTop: 16, color: '#666' }}>正在跳转...</div>
    </div>
  );
};

export default RootRedirect;
