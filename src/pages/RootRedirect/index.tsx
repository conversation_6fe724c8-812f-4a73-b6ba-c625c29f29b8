/**
 * 根路径重定向组件
 * 根据用户登录状态智能跳转到对应页面
 */
import { isLoggedIn, getUserInfo, isAdmin, hasRole } from '@/utils/auth';
import { history } from '@umijs/max';
import { Spin } from 'antd';
import React, { useEffect } from 'react';

/**
 * 获取登录后的默认跳转路径
 * 根据用户角色或权限返回不同的默认页面
 */
const getDefaultLoggedInPath = (): string => {
  try {
    const userInfo = getUserInfo();
    if (!userInfo) {
      return '/home';
    }

    // 根据用户类型或角色决定默认页面
    if (isAdmin()) {
      return '/home'; // 管理员默认到首页
    }

    if (hasRole('operator')) {
      return '/ice-order/dashboard'; // 操作员默认到数据看板
    }

    if (hasRole('viewer')) {
      return '/monitoring-report'; // 查看者默认到监控报表
    }

    // 根据用户类型决定
    if (userInfo.userType === 'supplier') {
      return '/ice-order/suppliers'; // 供应商用户默认到供应商管理
    }

    if (userInfo.userType === 'factory') {
      return '/ice-order/factories'; // 工厂用户默认到工厂管理
    }

  } catch (error) {
    console.warn('解析用户信息失败，使用默认路径:', error);
  }

  // 默认跳转到首页
  return '/home';
};

const RootRedirect: React.FC = () => {
  useEffect(() => {
    const performRedirect = () => {
      const userLoggedIn = isLoggedIn();

      // 开发环境调试日志
      if (process.env.NODE_ENV === 'development') {
        console.group('[Root Redirect Debug]');
        console.log('登录状态检查:', {
          isLoggedIn: userLoggedIn,
          userInfo: getUserInfo(),
          currentPath: window.location.pathname,
        });
        console.groupEnd();
      }

      if (userLoggedIn) {
        // 已登录，跳转到默认页面
        const defaultPath = getDefaultLoggedInPath();
        console.log(`用户已登录，跳转到: ${defaultPath}`);
        history.replace(defaultPath);
      } else {
        // 未登录，跳转到登录页
        console.log('用户未登录，跳转到登录页');
        history.replace('/login');
      }
    };

    // 使用 setTimeout 确保在组件挂载后执行重定向
    // 避免在 React 渲染过程中进行路由跳转
    const timer = setTimeout(performRedirect, 100);

    return () => clearTimeout(timer);
  }, []);

  // 显示加载状态，避免页面闪烁
  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column',
      }}
    >
      <Spin size="large" />
      <div style={{ marginTop: 16, color: '#666' }}>
        正在跳转...
      </div>
    </div>
  );
};

export default RootRedirect;
