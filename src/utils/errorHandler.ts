/**
 * 统一错误处理
 */
import { envConfig } from '@/config/env.config';
import { notification } from 'antd';
import { history } from 'umi';

// 错误类型枚举
export enum ErrorType {
  // 网络错误
  NETWORK = 'network',
  // 认证错误
  AUTH = 'auth',
  // 权限错误
  PERMISSION = 'permission',
  // 验证错误
  VALIDATION = 'validation',
  // 服务器错误
  SERVER = 'server',
  // 业务错误
  BUSINESS = 'business',
  // 未知错误
  UNKNOWN = 'unknown',
}

// 应用错误接口
export interface AppError {
  // 错误类型
  type: ErrorType;
  // 错误消息
  message: string;
  // 错误详情
  details?: string;
  // HTTP状态码
  statusCode?: number;
  // 原始错误
  originalError?: Error;
  // 错误数据
  data?: any;
}

// 创建应用错误
export function createAppError(
  type: ErrorType,
  message: string,
  details?: string,
  statusCode?: number,
  originalError?: Error,
  data?: any,
): AppError {
  return {
    type,
    message,
    details,
    statusCode,
    originalError,
    data,
  };
}

// 从HTTP状态码推断错误类型
export function getErrorTypeFromStatus(status: number): ErrorType {
  if (status === 401) return ErrorType.AUTH;
  if (status === 403) return ErrorType.PERMISSION;
  if (status === 400 || status === 422) return ErrorType.VALIDATION;
  if (status >= 500) return ErrorType.SERVER;
  return ErrorType.UNKNOWN;
}

// 处理错误
export function handleError(error: AppError | Error): void {
  // 转换普通Error为AppError
  const appError: AppError = error instanceof Error 
    ? { 
        type: ErrorType.UNKNOWN, 
        message: error.message,
        originalError: error,
      } 
    : error;
    
  // 开发环境打印详细错误信息
  if (envConfig.isDev) {
    console.group('[错误处理]');
    console.error('错误类型:', appError.type);
    console.error('错误消息:', appError.message);
    console.error('错误详情:', appError.details);
    console.error('状态码:', appError.statusCode);
    console.error('原始错误:', appError.originalError);
    console.error('错误数据:', appError.data);
    console.groupEnd();
  }
    
  // 根据错误类型处理
  switch (appError.type) {
    case ErrorType.AUTH:
      notification.error({
        message: '认证错误',
        description: appError.message || '您的登录已过期，请重新登录',
      });
      // 清除认证信息
      localStorage.removeItem('front_logix_token');
      // 跳转到登录页
      history.push('/login');
      break;
      
    case ErrorType.PERMISSION:
      notification.warning({
        message: '权限不足',
        description: appError.message || '您没有权限执行此操作',
      });
      break;
      
    case ErrorType.VALIDATION:
      notification.warning({
        message: '输入错误',
        description: appError.message || '请检查您的输入',
      });
      break;
      
    case ErrorType.SERVER:
      notification.error({
        message: '服务器错误',
        description: appError.message || '服务器处理请求时发生错误，请稍后再试',
      });
      break;
      
    case ErrorType.NETWORK:
      notification.error({
        message: '网络错误',
        description: appError.message || '网络连接异常，请检查您的网络设置',
      });
      break;
      
    case ErrorType.BUSINESS:
      notification.error({
        message: '业务错误',
        description: appError.message || '操作失败',
      });
      break;
      
    default:
      notification.error({
        message: '操作失败',
        description: appError.message || '发生未知错误',
      });
  }
}

// 导出默认处理函数
export default handleError;