/**
 * 兼容性请求函数
 * 临时解决方案：为了让项目快速构建成功，提供一个兼容的 request 函数
 * 后续应该逐步替换为直接使用 axios 客户端
 */
import { get, post, put, del } from './apiClient';
import { ApiResponse } from '@/types/api';

interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  params?: any;
  data?: any;
  [key: string]: any;
}

/**
 * 兼容的 request 函数
 * @deprecated 请使用 @/utils/apiClient 中的 get, post, put, del 函数
 */
export async function request<T = any>(
  url: string,
  options: RequestOptions = {}
): Promise<T> {
  const { method = 'GET', params, data, ...restOptions } = options;

  try {
    switch (method.toUpperCase()) {
      case 'GET':
        return await get(url, params, restOptions) as T;
      case 'POST':
        return await post(url, data, restOptions) as T;
      case 'PUT':
        return await put(url, data, restOptions) as T;
      case 'DELETE':
        return await del(url, params, restOptions) as T;
      default:
        throw new Error(`不支持的请求方法: ${method}`);
    }
  } catch (error) {
    console.error(`[Request Compat] 请求失败:`, { url, method, error });
    throw error;
  }
}

export default request;
