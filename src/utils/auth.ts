// 认证相关常量
const TOKEN_KEY = 'front_logix_token';
const USER_INFO_KEY = 'userInfo';
const REFRESH_TOKEN_KEY = 'refresh_token';
const LOGIN_TIME_KEY = 'login_time';
const LAST_ACTIVITY_KEY = 'last_activity';

// 支持的 token 存储 key 列表（兼容性）
const TOKEN_KEYS = [TOKEN_KEY, 'token', 'auth_token'];

// 会话配置
const SESSION_CONFIG = {
  // Token 过期时间（毫秒）- 24小时
  TOKEN_EXPIRE_TIME: 24 * 60 * 60 * 1000,
  // 无操作自动登出时间（毫秒）- 2小时
  IDLE_TIMEOUT: 2 * 60 * 60 * 1000,
  // Token 刷新阈值（毫秒）- 1小时
  REFRESH_THRESHOLD: 60 * 60 * 1000,
};

/**
 * 获取 token
 * 支持多种 token 存储方式的兼容性
 */
export function getToken(): string | null {
  console.log('🔑 [auth.ts] getToken() 被调用');

  for (const key of TOKEN_KEYS) {
    const token = localStorage.getItem(key);
    console.log(`🔑 [auth.ts] 检查 ${key}:`, token ? `${token.substring(0, 10)}...` : null);
    if (token && token.trim() !== '') {
      console.log(`🔑 [auth.ts] 找到有效 token，使用 ${key}`);
      return token;
    }
  }

  console.log('🔑 [auth.ts] 未找到有效 token');
  return null;
}

/**
 * 设置 token
 */
export function setToken(token: string): void {
  localStorage.setItem(TOKEN_KEY, token);
  // 记录登录时间
  localStorage.setItem(LOGIN_TIME_KEY, Date.now().toString());
  // 更新最后活动时间
  updateLastActivity();
}

/**
 * 设置刷新 token
 */
export function setRefreshToken(refreshToken: string): void {
  localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken);
}

/**
 * 获取刷新 token
 */
export function getRefreshToken(): string | null {
  return localStorage.getItem(REFRESH_TOKEN_KEY);
}

/**
 * 更新最后活动时间
 */
export function updateLastActivity(): void {
  localStorage.setItem(LAST_ACTIVITY_KEY, Date.now().toString());
}

/**
 * 获取登录时间
 */
export function getLoginTime(): number | null {
  const loginTime = localStorage.getItem(LOGIN_TIME_KEY);
  return loginTime ? parseInt(loginTime, 10) : null;
}

/**
 * 获取最后活动时间
 */
export function getLastActivity(): number | null {
  const lastActivity = localStorage.getItem(LAST_ACTIVITY_KEY);
  return lastActivity ? parseInt(lastActivity, 10) : null;
}

/**
 * 移除 token
 * 清除所有可能的 token 存储
 */
export function removeToken(): void {
  TOKEN_KEYS.forEach((key) => {
    localStorage.removeItem(key);
  });
  localStorage.removeItem(REFRESH_TOKEN_KEY);
  localStorage.removeItem(LOGIN_TIME_KEY);
  localStorage.removeItem(LAST_ACTIVITY_KEY);
}

/**
 * 检查是否有 token
 */
export function hasToken(): boolean {
  return !!getToken();
}

/**
 * 获取用户信息
 */
export function getUserInfo(): any | null {
  console.log('👤 [auth.ts] getUserInfo() 被调用');

  try {
    const userInfoStr = localStorage.getItem(USER_INFO_KEY);
    console.log('👤 [auth.ts] 原始用户信息字符串:', userInfoStr);

    if (userInfoStr) {
      const userInfo = JSON.parse(userInfoStr);
      console.log('👤 [auth.ts] 解析后的用户信息:', userInfo);
      return userInfo;
    }
  } catch (error) {
    console.error('❌ [auth.ts] 解析用户信息失败:', error);
  }

  console.log('👤 [auth.ts] 未找到用户信息');
  return null;
}

/**
 * 设置用户信息
 */
export function setUserInfo(userInfo: any): void {
  try {
    localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo));
  } catch (error) {
    console.error('保存用户信息失败:', error);
  }
}

/**
 * 移除用户信息
 */
export function removeUserInfo(): void {
  localStorage.removeItem(USER_INFO_KEY);
}

/**
 * 检查 token 是否过期
 */
export function isTokenExpired(): boolean {
  const loginTime = getLoginTime();
  if (!loginTime) return true;

  const now = Date.now();
  const elapsed = now - loginTime;

  return elapsed > SESSION_CONFIG.TOKEN_EXPIRE_TIME;
}

/**
 * 检查是否需要刷新 token
 */
export function shouldRefreshToken(): boolean {
  const loginTime = getLoginTime();
  if (!loginTime) return false;

  const now = Date.now();
  const elapsed = now - loginTime;

  return elapsed > SESSION_CONFIG.REFRESH_THRESHOLD;
}

/**
 * 检查用户是否空闲超时
 */
export function isIdleTimeout(): boolean {
  const lastActivity = getLastActivity();
  if (!lastActivity) return true;

  const now = Date.now();
  const elapsed = now - lastActivity;

  return elapsed > SESSION_CONFIG.IDLE_TIMEOUT;
}

/**
 * 检查用户是否已登录
 * 同时检查 token 和用户信息的有效性，以及会话状态
 */
export function isLoggedIn(): boolean {
  console.log('🔐 [auth.ts] isLoggedIn() 函数被调用');

  const token = getToken();
  const userInfo = getUserInfo();

  // 基础验证：必须同时有 token 和有效的用户信息
  const hasValidToken = !!(token && token.trim() !== '');
  const hasValidUserInfo = !!(
    userInfo &&
    (userInfo.name || userInfo.id || userInfo.username)
  );

  // 会话验证
  const tokenExpired = isTokenExpired();
  const idleTimeout = isIdleTimeout();

  const result = hasValidToken && hasValidUserInfo && !tokenExpired && !idleTimeout;

  console.log('🔐 [auth.ts] isLoggedIn() 检查结果:', {
    token: token ? `${token.substring(0, 10)}...` : null,
    hasValidToken,
    userInfo,
    hasValidUserInfo,
    tokenExpired,
    idleTimeout,
    finalResult: result
  });

  // 如果会话过期，自动清理
  if ((tokenExpired || idleTimeout) && hasValidToken) {
    console.log('🔐 [auth.ts] 会话过期，自动清理认证信息');
    logout();
    return false;
  }

  // 如果登录有效，更新最后活动时间
  if (result) {
    updateLastActivity();
  }

  return result;
}

/**
 * 完整登出
 * 清除所有认证相关的本地存储
 */
export function logout(): void {
  removeToken();
  removeUserInfo();

  // 清除其他可能的认证相关存储
  const keysToRemove = ['access_token', 'refresh_token', 'user_permissions'];
  keysToRemove.forEach((key) => {
    localStorage.removeItem(key);
  });
}

/**
 * 获取用户角色
 */
export function getUserRoles(): string[] {
  const userInfo = getUserInfo();
  if (userInfo && userInfo.roles && Array.isArray(userInfo.roles)) {
    return userInfo.roles;
  }
  return [];
}

/**
 * 检查用户是否有指定角色
 */
export function hasRole(role: string): boolean {
  const roles = getUserRoles();
  return roles.includes(role);
}

/**
 * 检查用户是否是管理员
 */
export function isAdmin(): boolean {
  const userInfo = getUserInfo();
  if (!userInfo) return false;

  return !!(
    userInfo.isAdmin ||
    userInfo.userType === 'admin' ||
    hasRole('admin')
  );
}
