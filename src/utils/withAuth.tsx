/**
 * 认证高阶组件
 * 用于包装需要认证的页面组件
 */
import AuthGuard from '@/components/AuthGuard';
import React from 'react';

interface WithAuthOptions {
  /**
   * 是否需要登录
   * @default true
   */
  requireAuth?: boolean;
  /**
   * 未登录时的重定向路径
   * @default '/login'
   */
  redirectTo?: string;
  /**
   * 检查权限的函数
   */
  checkPermission?: () => boolean;
  /**
   * 权限不足时的重定向路径
   * @default '/403'
   */
  noPermissionRedirectTo?: string;
}

/**
 * 认证高阶组件
 * 
 * 使用示例：
 * ```tsx
 * const ProtectedPage = withAuth(MyPage, {
 *   requireAuth: true,
 *   checkPermission: () => hasRole('admin')
 * });
 * ```
 */
export function withAuth<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: WithAuthOptions = {}
) {
  const WithAuthComponent: React.FC<P> = (props) => {
    return (
      <AuthGuard {...options}>
        <WrappedComponent {...props} />
      </AuthGuard>
    );
  };

  // 设置显示名称，便于调试
  WithAuthComponent.displayName = `withAuth(${WrappedComponent.displayName || WrappedComponent.name})`;

  return WithAuthComponent;
}

/**
 * 管理员权限高阶组件
 * 只有管理员才能访问的页面
 */
export function withAdminAuth<P extends object>(
  WrappedComponent: React.ComponentType<P>
) {
  return withAuth(WrappedComponent, {
    requireAuth: true,
    checkPermission: () => {
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
      return !!(userInfo.isAdmin || userInfo.userType === 'admin');
    },
  });
}

/**
 * 角色权限高阶组件
 * 需要特定角色才能访问的页面
 */
export function withRoleAuth<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  requiredRoles: string[]
) {
  return withAuth(WrappedComponent, {
    requireAuth: true,
    checkPermission: () => {
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
      const userRoles = userInfo.roles || [];
      return requiredRoles.some(role => userRoles.includes(role));
    },
  });
}

export default withAuth;
